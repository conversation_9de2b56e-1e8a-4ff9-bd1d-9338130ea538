using UnityEngine;
using UnityEngine.AI;

/// <summary>
/// Receives and executes voice commands for AI teammates
/// </summary>
public class VoiceCommandReceiver : MonoBehaviour
{
    [Header("Command Response")]
    public bool respondToCommands = true;
    public float commandPriority = 10f; // Higher than normal AI priorities
    public float commandDuration = 5f; // How long to execute command before returning to normal AI
    
    private TeammateAI teammateAI;
    private NavMeshAgent agent;
    private AIAudioAnnouncer audioAnnouncer;
    private SquadMemberCoordinator squadCoordinator;
    private AIActionLogger actionLogger;
    
    private VoiceCommandSystem.CommandType currentCommand = VoiceCommandSystem.CommandType.None;
    private VoiceCommandSystem.CommandData activeCommandData;
    private float commandStartTime = 0f;
    private bool isExecutingCommand = false;
    
    // Store original AI state to restore later
    private TeammateAI.AIState originalAIState;
    
    void Start()
    {
        teammateAI = GetComponent<TeammateAI>();
        agent = GetComponent<NavMeshAgent>();
        audioAnnouncer = GetComponent<AIAudioAnnouncer>();
        squadCoordinator = GetComponent<SquadMemberCoordinator>();
        
        actionLogger = FindObjectOfType<AIActionLogger>();
        
        Debug.Log($"Voice Command Receiver initialized for {gameObject.name}");
    }
    
    void Update()
    {
        if (isExecutingCommand)
        {
            // Check if command has expired
            if (Time.time - commandStartTime > commandDuration)
            {
                CompleteCommand();
            }
            else
            {
                // Continue executing command
                ExecuteActiveCommand();
            }
        }
    }
    
    public void ReceiveCommand(VoiceCommandSystem.CommandData commandData)
    {
        if (!respondToCommands) return;
        
        // Store current AI state if not already executing a command
        if (!isExecutingCommand && teammateAI != null)
        {
            originalAIState = teammateAI.currentState;
        }
        
        // Set new command
        currentCommand = commandData.command;
        activeCommandData = commandData;
        commandStartTime = Time.time;
        isExecutingCommand = true;
        
        // Notify squad coordinator
        if (squadCoordinator != null)
        {
            squadCoordinator.StartSpecialAction($"voice_command_{commandData.command}");
        }
        
        // Audio acknowledgment
        if (audioAnnouncer != null)
        {
            audioAnnouncer.RespondToCommand(commandData.command.ToString());
        }
        
        // Log the command execution
        if (actionLogger != null)
        {
            actionLogger.LogAction(gameObject.name, $"Executing voice command: {commandData.command}");
        }
        
        Debug.Log($"🎤 {gameObject.name} received command: {commandData.command}");
        
        // Start executing immediately
        ExecuteActiveCommand();
    }
    
    void ExecuteActiveCommand()
    {
        if (!isExecutingCommand || agent == null) return;
        
        switch (currentCommand)
        {
            case VoiceCommandSystem.CommandType.Follow:
                ExecuteFollowCommand();
                break;
                
            case VoiceCommandSystem.CommandType.Revive:
                ExecuteReviveCommand();
                break;
                
            case VoiceCommandSystem.CommandType.Attack:
                ExecuteAttackCommand();
                break;
                
            case VoiceCommandSystem.CommandType.Defend:
                ExecuteDefendCommand();
                break;
                
            case VoiceCommandSystem.CommandType.Reload:
                ExecuteReloadCommand();
                break;
                
            case VoiceCommandSystem.CommandType.Formation:
                ExecuteFormationCommand();
                break;
        }
    }
    
    void ExecuteFollowCommand()
    {
        // Force AI to follow player immediately
        if (teammateAI != null)
        {
            teammateAI.currentState = TeammateAI.AIState.Following;
        }
        
        // Move to player position or specified target
        Vector3 targetPos = activeCommandData.targetPosition;
        if (targetPos == Vector3.zero)
        {
            PlayerController player = FindObjectOfType<PlayerController>();
            if (player != null)
            {
                targetPos = player.transform.position;
            }
        }
        
        if (agent.isOnNavMesh)
        {
            agent.SetDestination(targetPos);
        }
        
        Debug.Log($"{gameObject.name} following to {targetPos}");
    }
    
    void ExecuteReviveCommand()
    {
        // Force AI to prioritize reviving
        if (teammateAI != null)
        {
            teammateAI.currentState = TeammateAI.AIState.Reviving;
        }
        
        // Find player and move to revive
        PlayerController player = FindObjectOfType<PlayerController>();
        if (player != null)
        {
            ReviveSystem reviveSystem = player.GetComponent<ReviveSystem>();
            if (reviveSystem != null && reviveSystem.isDown)
            {
                if (agent.isOnNavMesh)
                {
                    agent.SetDestination(player.transform.position);
                }
                Debug.Log($"{gameObject.name} moving to revive player");
            }
            else
            {
                Debug.Log($"{gameObject.name} received revive command but player doesn't need revival");
                CompleteCommand(); // End command early if not needed
            }
        }
    }
    
    void ExecuteAttackCommand()
    {
        // Force AI into combat mode
        if (teammateAI != null)
        {
            teammateAI.currentState = TeammateAI.AIState.Combat;
        }
        
        // Find nearest enemy or use specified target
        Transform target = activeCommandData.targetObject;
        if (target == null)
        {
            // Find nearest enemy
            EnemyAI[] enemies = FindObjectsOfType<EnemyAI>();
            float nearestDistance = float.MaxValue;
            
            foreach (EnemyAI enemy in enemies)
            {
                if (enemy != null)
                {
                    float distance = Vector3.Distance(transform.position, enemy.transform.position);
                    if (distance < nearestDistance)
                    {
                        target = enemy.transform;
                        nearestDistance = distance;
                    }
                }
            }
        }
        
        if (target != null && agent.isOnNavMesh)
        {
            agent.SetDestination(target.position);
            Debug.Log($"{gameObject.name} attacking target: {target.name}");
        }
        else
        {
            Debug.Log($"{gameObject.name} received attack command but no targets found");
        }
    }
    
    void ExecuteDefendCommand()
    {
        // Force AI to take cover or defensive position
        if (teammateAI != null)
        {
            teammateAI.currentState = TeammateAI.AIState.TakingCover;
        }
        
        // Find cover near player or specified position
        Vector3 defendPosition = activeCommandData.targetPosition;
        if (defendPosition == Vector3.zero)
        {
            PlayerController player = FindObjectOfType<PlayerController>();
            if (player != null)
            {
                defendPosition = player.transform.position;
            }
        }
        
        // Find cover near defend position
        Vector3 coverPosition = FindCoverNear(defendPosition);
        if (coverPosition != Vector3.zero && agent.isOnNavMesh)
        {
            agent.SetDestination(coverPosition);
            Debug.Log($"{gameObject.name} taking defensive position at {coverPosition}");
        }
    }
    
    void ExecuteReloadCommand()
    {
        // Stop current actions and simulate reload
        if (agent.isOnNavMesh)
        {
            agent.ResetPath();
        }
        
        // Look for ammo loot nearby
        LootItem[] lootItems = FindObjectsOfType<LootItem>();
        LootItem nearestAmmo = null;
        float nearestDistance = float.MaxValue;
        
        foreach (LootItem loot in lootItems)
        {
            if (loot.lootType == LootItem.LootType.Ammo)
            {
                float distance = Vector3.Distance(transform.position, loot.transform.position);
                if (distance < nearestDistance)
                {
                    nearestAmmo = loot;
                    nearestDistance = distance;
                }
            }
        }
        
        if (nearestAmmo != null && agent.isOnNavMesh)
        {
            agent.SetDestination(nearestAmmo.transform.position);
            Debug.Log($"{gameObject.name} moving to collect ammo");
        }
        else
        {
            Debug.Log($"{gameObject.name} reloading in place (no ammo found)");
        }
    }
    
    void ExecuteFormationCommand()
    {
        // Get formation position from squad manager
        SquadManager squadManager = FindObjectOfType<SquadManager>();
        if (squadManager != null && teammateAI != null)
        {
            Vector3 formationPos = squadManager.GetFormationPosition(teammateAI);
            if (formationPos != Vector3.zero && agent.isOnNavMesh)
            {
                agent.SetDestination(formationPos);
                Debug.Log($"{gameObject.name} moving to formation position");
            }
        }
        else
        {
            // Fallback: move to player
            ExecuteFollowCommand();
        }
    }
    
    Vector3 FindCoverNear(Vector3 position)
    {
        // Simple cover finding - look for objects with cover layer
        Collider[] covers = Physics.OverlapSphere(position, 10f, LayerMask.GetMask("Cover"));
        if (covers.Length > 0)
        {
            return covers[0].transform.position;
        }
        
        // Fallback: position behind player relative to threats
        EnemyAI[] enemies = FindObjectsOfType<EnemyAI>();
        if (enemies.Length > 0)
        {
            Vector3 threatDirection = (enemies[0].transform.position - position).normalized;
            return position - threatDirection * 3f; // 3 units behind player
        }
        
        return Vector3.zero;
    }
    
    void CompleteCommand()
    {
        if (!isExecutingCommand) return;
        
        // Log command completion
        if (actionLogger != null)
        {
            actionLogger.LogAction(gameObject.name, $"Completed voice command: {currentCommand}");
        }
        
        // Notify squad coordinator
        if (squadCoordinator != null)
        {
            squadCoordinator.EndSpecialAction();
        }
        
        // Restore original AI state
        if (teammateAI != null)
        {
            teammateAI.currentState = originalAIState;
        }
        
        // Reset command state
        currentCommand = VoiceCommandSystem.CommandType.None;
        isExecutingCommand = false;
        
        Debug.Log($"✅ {gameObject.name} completed voice command");
    }
    
    // Public methods for external control
    public bool IsExecutingCommand() => isExecutingCommand;
    public VoiceCommandSystem.CommandType GetCurrentCommand() => currentCommand;
    public void CancelCommand() => CompleteCommand();
    
    // Force complete command (for emergency situations)
    public void ForceCompleteCommand()
    {
        CompleteCommand();
    }
}
