using UnityEngine;

/// <summary>
/// Loot item that can be collected by AI teammates and players
/// </summary>
public class LootItem : MonoBehaviour
{
    [Header("Loot Settings")]
    public LootType lootType = LootType.Ammo;
    public int value = 1;
    public bool autoCollect = true;
    public float collectRange = 2f;
    
    [Header("Visual Settings")]
    public bool rotateItem = true;
    public float rotationSpeed = 90f;
    public bool bobItem = true;
    public float bobHeight = 0.5f;
    public float bobSpeed = 2f;
    
    [Header("Audio")]
    public AudioClip collectSound;
    public float collectVolume = 1f;
    
    [Header("Effects")]
    public GameObject collectEffect;
    public Color glowColor = Color.yellow;
    public bool enableGlow = true;
    
    private Vector3 startPosition;
    private Renderer itemRenderer;
    private Material originalMaterial;
    private bool isCollected = false;
    
    public enum LootType
    {
        Ammo,
        Medkit,
        Armor,
        Weapon,
        Key,
        Coin
    }
    
    void Start()
    {
        startPosition = transform.position;
        itemRenderer = GetComponent<Renderer>();
        
        // Setup visual effects
        if (itemRenderer != null && enableGlow)
        {
            originalMaterial = itemRenderer.material;
            SetupGlowEffect();
        }
        
        // Setup collider for collection
        Collider collider = GetComponent<Collider>();
        if (collider == null)
        {
            collider = gameObject.AddComponent<SphereCollider>();
            collider.isTrigger = true;
        }
        else
        {
            collider.isTrigger = true;
        }
        
        // Set appropriate layer
        gameObject.layer = 9; // Loot layer
        
        // Set tag for easy identification
        if (gameObject.tag == "Untagged")
        {
            gameObject.tag = "Loot";
        }
        
        Debug.Log($"Loot item created: {lootType} (Value: {value})");
    }
    
    void Update()
    {
        if (isCollected) return;
        
        // Rotation animation
        if (rotateItem)
        {
            transform.Rotate(Vector3.up, rotationSpeed * Time.deltaTime);
        }
        
        // Bobbing animation
        if (bobItem)
        {
            float newY = startPosition.y + Mathf.Sin(Time.time * bobSpeed) * bobHeight;
            transform.position = new Vector3(transform.position.x, newY, transform.position.z);
        }
        
        // Auto-collect check
        if (autoCollect)
        {
            CheckForCollectors();
        }
    }
    
    void CheckForCollectors()
    {
        // Check for player
        PlayerController player = FindObjectOfType<PlayerController>();
        if (player != null)
        {
            float distanceToPlayer = Vector3.Distance(transform.position, player.transform.position);
            if (distanceToPlayer <= collectRange)
            {
                CollectByPlayer(player);
                return;
            }
        }
        
        // Check for AI teammates
        TeammateAI[] teammates = FindObjectsOfType<TeammateAI>();
        foreach (TeammateAI teammate in teammates)
        {
            float distanceToTeammate = Vector3.Distance(transform.position, teammate.transform.position);
            if (distanceToTeammate <= collectRange)
            {
                CollectByTeammate(teammate);
                return;
            }
        }
    }
    
    void SetupGlowEffect()
    {
        if (itemRenderer != null)
        {
            Material glowMaterial = new Material(itemRenderer.material);
            glowMaterial.color = glowColor;
            glowMaterial.SetFloat("_Metallic", 0f);
            glowMaterial.SetFloat("_Glossiness", 0.8f);
            
            // Add emission if supported
            if (glowMaterial.HasProperty("_EmissionColor"))
            {
                glowMaterial.EnableKeyword("_EMISSION");
                glowMaterial.SetColor("_EmissionColor", glowColor * 0.5f);
            }
            
            itemRenderer.material = glowMaterial;
        }
    }
    
    public void CollectByPlayer(PlayerController player)
    {
        if (isCollected) return;
        
        Debug.Log($"Player collected {lootType} (Value: {value})");
        
        // Apply loot effect to player
        switch (lootType)
        {
            case LootType.Ammo:
                // Add ammo to player (implement in PlayerController)
                break;
            case LootType.Medkit:
                // Heal player using PlayerController's built-in health system
                player.Heal(value * 25f); // 25 HP per medkit value
                break;
            case LootType.Armor:
                // Add armor to player
                break;
        }
        
        Collect();
    }
    
    public void CollectByTeammate(TeammateAI teammate)
    {
        if (isCollected) return;
        
        Debug.Log($"AI Teammate collected {lootType} (Value: {value})");
        
        // Notify AI audio announcer
        AIAudioAnnouncer audioAnnouncer = teammate.GetComponent<AIAudioAnnouncer>();
        if (audioAnnouncer != null)
        {
            audioAnnouncer.PlayLootFound();
        }
        
        // Apply loot effect to teammate
        switch (lootType)
        {
            case LootType.Ammo:
                // AI doesn't need ammo, but can collect for player
                break;
            case LootType.Medkit:
                // AI can use medkits if it has health system
                break;
        }
        
        Collect();
    }
    
    void Collect()
    {
        if (isCollected) return;
        
        isCollected = true;
        
        // Play collect sound
        if (collectSound != null)
        {
            AudioSource.PlayClipAtPoint(collectSound, transform.position, collectVolume);
        }
        
        // Spawn collect effect
        if (collectEffect != null)
        {
            GameObject effect = Instantiate(collectEffect, transform.position, Quaternion.identity);
            Destroy(effect, 3f);
        }
        
        // Destroy the loot item
        Destroy(gameObject);
    }
    
    void OnTriggerEnter(Collider other)
    {
        if (isCollected) return;
        
        // Check if player entered
        PlayerController player = other.GetComponent<PlayerController>();
        if (player != null)
        {
            CollectByPlayer(player);
            return;
        }
        
        // Check if AI teammate entered
        TeammateAI teammate = other.GetComponent<TeammateAI>();
        if (teammate != null)
        {
            CollectByTeammate(teammate);
            return;
        }
    }
    
    // Static method to create loot items
    public static GameObject CreateLootItem(LootType type, Vector3 position, int value = 1)
    {
        GameObject lootObj = GameObject.CreatePrimitive(PrimitiveType.Cube);
        lootObj.transform.position = position;
        lootObj.transform.localScale = Vector3.one * 0.5f;
        lootObj.name = $"Loot_{type}_{Random.Range(1000, 9999)}";
        
        LootItem lootItem = lootObj.AddComponent<LootItem>();
        lootItem.lootType = type;
        lootItem.value = value;
        
        // Set color based on type
        Renderer renderer = lootObj.GetComponent<Renderer>();
        if (renderer != null)
        {
            switch (type)
            {
                case LootType.Ammo:
                    renderer.material.color = Color.yellow;
                    lootItem.glowColor = Color.yellow;
                    break;
                case LootType.Medkit:
                    renderer.material.color = Color.green;
                    lootItem.glowColor = Color.green;
                    break;
                case LootType.Armor:
                    renderer.material.color = Color.blue;
                    lootItem.glowColor = Color.blue;
                    break;
                case LootType.Weapon:
                    renderer.material.color = Color.red;
                    lootItem.glowColor = Color.red;
                    break;
                case LootType.Key:
                    renderer.material.color = Color.magenta;
                    lootItem.glowColor = Color.magenta;
                    break;
                case LootType.Coin:
                    renderer.material.color = Color.cyan;
                    lootItem.glowColor = Color.cyan;
                    break;
            }
        }
        
        return lootObj;
    }
    
    void OnDrawGizmosSelected()
    {
        // Draw collection range
        Gizmos.color = Color.green;
        Gizmos.DrawWireSphere(transform.position, collectRange);
    }
}
