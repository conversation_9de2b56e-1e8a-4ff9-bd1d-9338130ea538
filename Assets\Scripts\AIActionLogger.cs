using UnityEngine;
using System.IO;
using System.Collections.Generic;
using System;

/// <summary>
/// Comprehensive AI Action Logger for behavior analysis and Augment integration
/// Logs all AI actions, decisions, and outcomes to JSON for analysis
/// </summary>
public class AIActionLogger : MonoBehaviour
{
    [Header("Logging Settings")]
    public bool enableLogging = true;
    public bool logToFile = true;
    public bool logToConsole = false;
    public int maxLogEntries = 1000;
    public float autoSaveInterval = 30f; // Save every 30 seconds
    
    [Header("Log Categories")]
    public bool logMovement = true;
    public bool logCombat = true;
    public bool logCommands = true;
    public bool logDecisions = true;
    public bool logInteractions = true;
    
    private string logFilePath;
    private List<LogEntry> logEntries = new List<LogEntry>();
    private float lastSaveTime = 0f;
    
    [System.Serializable]
    public class LogEntry
    {
        public string timestamp;
        public string agentName;
        public string category;
        public string action;
        public string details;
        public Vector3 position;
        public string targetName;
        public Vector3 targetPosition;
        public float duration;
        public bool success;
        public string metadata;
    }
    
    [System.Serializable]
    public class SessionData
    {
        public string sessionId;
        public string startTime;
        public string endTime;
        public int totalActions;
        public List<LogEntry> entries;
        public Dictionary<string, int> actionCounts;
        public Dictionary<string, float> successRates;
    }
    
    private SessionData currentSession;
    
    void Start()
    {
        InitializeLogger();
    }
    
    void Update()
    {
        // Auto-save periodically
        if (logToFile && Time.time - lastSaveTime > autoSaveInterval)
        {
            SaveLogToFile();
            lastSaveTime = Time.time;
        }
    }
    
    void InitializeLogger()
    {
        // Create log file path
        string timestamp = DateTime.Now.ToString("yyyy-MM-dd_HH-mm-ss");
        logFilePath = Path.Combine(Application.persistentDataPath, $"ai_action_log_{timestamp}.json");
        
        // Initialize session
        currentSession = new SessionData
        {
            sessionId = Guid.NewGuid().ToString(),
            startTime = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss UTC"),
            entries = new List<LogEntry>(),
            actionCounts = new Dictionary<string, int>(),
            successRates = new Dictionary<string, float>()
        };
        
        Debug.Log($"📄 AI Action Logger initialized. Log file: {logFilePath}");
        
        // Log session start
        LogAction("System", "Logger", "Session started", "", Vector3.zero, "", Vector3.zero, 0f, true);
    }
    
    public void LogAction(string agentName, string category, string action, string details = "", 
                         Vector3 position = default, string targetName = "", Vector3 targetPosition = default, 
                         float duration = 0f, bool success = true, string metadata = "")
    {
        if (!enableLogging) return;
        
        // Check category filters
        if (!ShouldLogCategory(category)) return;
        
        LogEntry entry = new LogEntry
        {
            timestamp = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss.fff UTC"),
            agentName = agentName,
            category = category,
            action = action,
            details = details,
            position = position,
            targetName = targetName,
            targetPosition = targetPosition,
            duration = duration,
            success = success,
            metadata = metadata
        };
        
        // Add to current session
        logEntries.Add(entry);
        currentSession.entries.Add(entry);
        
        // Update statistics
        UpdateActionStatistics(action, success);
        
        // Console logging
        if (logToConsole)
        {
            Debug.Log($"📄 [{category}] {agentName}: {action} - {details}");
        }
        
        // Manage memory
        if (logEntries.Count > maxLogEntries)
        {
            logEntries.RemoveAt(0);
        }
    }
    
    public void LogCommand(string issuer, string command, VoiceCommandSystem.CommandData commandData)
    {
        string details = $"Command: {command}, Target: {commandData.targetPosition}";
        if (!string.IsNullOrEmpty(commandData.voiceText))
        {
            details += $", Voice: '{commandData.voiceText}'";
        }
        
        LogAction(issuer, "Command", $"Issued_{command}", details, 
                 commandData.targetPosition, commandData.targetObject?.name ?? "", 
                 commandData.targetObject?.position ?? Vector3.zero, 0f, true, 
                 JsonUtility.ToJson(commandData));
    }
    
    public void LogDecision(string agentName, string fromState, string toState, string reason, bool success = true)
    {
        string details = $"State transition: {fromState} → {toState}. Reason: {reason}";
        LogAction(agentName, "Decision", "StateChange", details, Vector3.zero, "", Vector3.zero, 0f, success);
    }
    
    public void LogCombat(string agentName, string action, string targetName, Vector3 targetPos, float damage = 0f, bool hit = true)
    {
        string details = $"Action: {action}, Damage: {damage}, Hit: {hit}";
        LogAction(agentName, "Combat", action, details, Vector3.zero, targetName, targetPos, 0f, hit);
    }
    
    public void LogMovement(string agentName, Vector3 fromPos, Vector3 toPos, string reason, float duration, bool completed = true)
    {
        string details = $"Movement: {reason}, Distance: {Vector3.Distance(fromPos, toPos):F2}m";
        LogAction(agentName, "Movement", "Navigate", details, fromPos, "", toPos, duration, completed);
    }
    
    public void LogInteraction(string agentName, string interactionType, string targetName, bool success = true)
    {
        string details = $"Interaction: {interactionType} with {targetName}";
        LogAction(agentName, "Interaction", interactionType, details, Vector3.zero, targetName, Vector3.zero, 0f, success);
    }
    
    bool ShouldLogCategory(string category)
    {
        switch (category.ToLower())
        {
            case "movement": return logMovement;
            case "combat": return logCombat;
            case "command": return logCommands;
            case "decision": return logDecisions;
            case "interaction": return logInteractions;
            default: return true; // Log unknown categories by default
        }
    }
    
    void UpdateActionStatistics(string action, bool success)
    {
        // Update action counts
        if (currentSession.actionCounts.ContainsKey(action))
        {
            currentSession.actionCounts[action]++;
        }
        else
        {
            currentSession.actionCounts[action] = 1;
        }
        
        // Update success rates
        string successKey = $"{action}_success";
        string totalKey = $"{action}_total";
        
        if (!currentSession.successRates.ContainsKey(totalKey))
        {
            currentSession.successRates[totalKey] = 0f;
            currentSession.successRates[successKey] = 0f;
        }
        
        currentSession.successRates[totalKey]++;
        if (success)
        {
            currentSession.successRates[successKey]++;
        }
    }
    
    public void SaveLogToFile()
    {
        if (!logToFile || logEntries.Count == 0) return;
        
        try
        {
            // Update session end time
            currentSession.endTime = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss UTC");
            currentSession.totalActions = logEntries.Count;
            
            // Convert to JSON
            string json = JsonUtility.ToJson(currentSession, true);
            
            // Write to file
            File.WriteAllText(logFilePath, json);
            
            Debug.Log($"📄 AI Action Log saved: {logEntries.Count} entries to {logFilePath}");
        }
        catch (Exception e)
        {
            Debug.LogError($"Failed to save AI Action Log: {e.Message}");
        }
    }
    
    public void ExportForAugment()
    {
        // Create Augment-friendly export
        var augmentData = new
        {
            sessionId = currentSession.sessionId,
            timestamp = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss UTC"),
            summary = new
            {
                totalActions = logEntries.Count,
                actionTypes = currentSession.actionCounts,
                successRates = CalculateSuccessRates(),
                mostCommonActions = GetMostCommonActions(5),
                recommendations = GenerateRecommendations()
            },
            rawData = logEntries.GetRange(Mathf.Max(0, logEntries.Count - 100), Mathf.Min(100, logEntries.Count)) // Last 100 entries
        };
        
        string augmentPath = Path.Combine(Application.persistentDataPath, "augment_ai_analysis.json");
        string json = JsonUtility.ToJson(augmentData, true);
        File.WriteAllText(augmentPath, json);
        
        Debug.Log($"🧠 Augment analysis data exported to: {augmentPath}");
    }
    
    Dictionary<string, float> CalculateSuccessRates()
    {
        var rates = new Dictionary<string, float>();
        
        foreach (var kvp in currentSession.successRates)
        {
            if (kvp.Key.EndsWith("_total"))
            {
                string action = kvp.Key.Replace("_total", "");
                string successKey = $"{action}_success";
                
                if (currentSession.successRates.ContainsKey(successKey))
                {
                    float successRate = currentSession.successRates[successKey] / kvp.Value;
                    rates[action] = successRate;
                }
            }
        }
        
        return rates;
    }
    
    List<string> GetMostCommonActions(int count)
    {
        var sortedActions = new List<KeyValuePair<string, int>>(currentSession.actionCounts);
        sortedActions.Sort((x, y) => y.Value.CompareTo(x.Value));
        
        var result = new List<string>();
        for (int i = 0; i < Mathf.Min(count, sortedActions.Count); i++)
        {
            result.Add($"{sortedActions[i].Key} ({sortedActions[i].Value})");
        }
        
        return result;
    }
    
    List<string> GenerateRecommendations()
    {
        var recommendations = new List<string>();
        var successRates = CalculateSuccessRates();
        
        foreach (var kvp in successRates)
        {
            if (kvp.Value < 0.5f) // Less than 50% success rate
            {
                recommendations.Add($"Improve {kvp.Key} success rate (currently {kvp.Value:P})");
            }
        }
        
        if (recommendations.Count == 0)
        {
            recommendations.Add("AI performance is optimal");
        }
        
        return recommendations;
    }
    
    void OnApplicationPause(bool pauseStatus)
    {
        if (pauseStatus)
        {
            SaveLogToFile();
        }
    }
    
    void OnApplicationQuit()
    {
        SaveLogToFile();
        ExportForAugment();
    }
    
    // Public getters for external systems
    public int GetTotalLogEntries() => logEntries.Count;
    public List<LogEntry> GetRecentEntries(int count) => logEntries.GetRange(Mathf.Max(0, logEntries.Count - count), Mathf.Min(count, logEntries.Count));
    public Dictionary<string, int> GetActionCounts() => new Dictionary<string, int>(currentSession.actionCounts);
    public string GetLogFilePath() => logFilePath;
}
