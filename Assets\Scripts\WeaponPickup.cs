using UnityEngine;

/// <summary>
/// Weapon pickup system for PUBG-style loot
/// Allows players and AI to pick up weapons
/// </summary>
public class WeaponPickup : MonoBehaviour
{
    [Header("Weapon Settings")]
    public WeaponSystem.WeaponType weaponType = WeaponSystem.WeaponType.AssaultRifle;
    public int ammoAmount = 30;
    public bool autoPickup = true;
    public float pickupRange = 2f;
    
    [Header("Visual Settings")]
    public bool rotateWeapon = true;
    public float rotationSpeed = 45f;
    public bool bobWeapon = true;
    public float bobHeight = 0.2f;
    public float bobSpeed = 2f;
    
    [Header("Audio")]
    public AudioClip pickupSound;
    public float pickupVolume = 1f;
    
    [Header("Effects")]
    public GameObject pickupEffect;
    public Color glowColor = Color.yellow;
    public bool enableGlow = true;
    
    private Vector3 startPosition;
    private Renderer weaponRenderer;
    private Material originalMaterial;
    private bool isPickedUp = false;
    
    void Start()
    {
        startPosition = transform.position;
        weaponRenderer = GetComponent<Renderer>();
        
        // Setup visual effects
        if (weaponRenderer != null && enableGlow)
        {
            originalMaterial = weaponRenderer.material;
            SetupGlowEffect();
        }
        
        // Setup collider for pickup
        Collider collider = GetComponent<Collider>();
        if (collider == null)
        {
            collider = gameObject.AddComponent<BoxCollider>();
            collider.isTrigger = true;
        }
        else
        {
            collider.isTrigger = true;
        }
        
        // Set appropriate layer
        gameObject.layer = 9; // Loot layer
        
        // Set tag for easy identification
        if (gameObject.tag == "Untagged")
        {
            gameObject.tag = "Weapon";
        }
        
        Debug.Log($"Weapon pickup created: {weaponType} with {ammoAmount} ammo");
    }
    
    void Update()
    {
        if (isPickedUp) return;
        
        // Rotation animation
        if (rotateWeapon)
        {
            transform.Rotate(Vector3.up, rotationSpeed * Time.deltaTime);
        }
        
        // Bobbing animation
        if (bobWeapon)
        {
            float newY = startPosition.y + Mathf.Sin(Time.time * bobSpeed) * bobHeight;
            transform.position = new Vector3(transform.position.x, newY, transform.position.z);
        }
        
        // Auto-pickup check
        if (autoPickup)
        {
            CheckForPickup();
        }
    }
    
    void CheckForPickup()
    {
        // Check for player
        PlayerController player = FindObjectOfType<PlayerController>();
        if (player != null)
        {
            float distanceToPlayer = Vector3.Distance(transform.position, player.transform.position);
            if (distanceToPlayer <= pickupRange)
            {
                PickupByPlayer(player);
                return;
            }
        }
        
        // Check for AI agents
        SquadMateAgent[] agents = FindObjectsOfType<SquadMateAgent>();
        foreach (SquadMateAgent agent in agents)
        {
            float distanceToAgent = Vector3.Distance(transform.position, agent.transform.position);
            if (distanceToAgent <= pickupRange)
            {
                PickupByAgent(agent);
                return;
            }
        }
        
        // Check for AI teammates
        TeammateAI[] teammates = FindObjectsOfType<TeammateAI>();
        foreach (TeammateAI teammate in teammates)
        {
            float distanceToTeammate = Vector3.Distance(transform.position, teammate.transform.position);
            if (distanceToTeammate <= pickupRange)
            {
                PickupByTeammate(teammate);
                return;
            }
        }
    }
    
    void SetupGlowEffect()
    {
        if (weaponRenderer != null)
        {
            Material glowMaterial = new Material(weaponRenderer.material);
            glowMaterial.color = glowColor;
            
            // Add emission if supported
            if (glowMaterial.HasProperty("_EmissionColor"))
            {
                glowMaterial.EnableKeyword("_EMISSION");
                glowMaterial.SetColor("_EmissionColor", glowColor * 0.3f);
            }
            
            weaponRenderer.material = glowMaterial;
        }
    }
    
    public void PickupByPlayer(PlayerController player)
    {
        if (isPickedUp) return;
        
        WeaponSystem playerWeaponSystem = player.GetComponent<WeaponSystem>();
        if (playerWeaponSystem != null)
        {
            playerWeaponSystem.EquipWeapon(weaponType);
            playerWeaponSystem.AddAmmo(ammoAmount);
            
            Debug.Log($"Player picked up {weaponType} with {ammoAmount} ammo");
        }
        
        Pickup();
    }
    
    public void PickupByAgent(SquadMateAgent agent)
    {
        if (isPickedUp) return;
        
        WeaponSystem agentWeaponSystem = agent.GetComponent<WeaponSystem>();
        if (agentWeaponSystem != null)
        {
            agentWeaponSystem.EquipWeapon(weaponType);
            agentWeaponSystem.AddAmmo(ammoAmount);
            
            // Notify agent for ML training
            agent.OnLootCollected(LootItem.LootType.Weapon);
            
            Debug.Log($"Agent {agent.name} picked up {weaponType} with {ammoAmount} ammo");
        }
        
        Pickup();
    }
    
    public void PickupByTeammate(TeammateAI teammate)
    {
        if (isPickedUp) return;
        
        WeaponSystem teammateWeaponSystem = teammate.GetComponent<WeaponSystem>();
        if (teammateWeaponSystem != null)
        {
            teammateWeaponSystem.EquipWeapon(weaponType);
            teammateWeaponSystem.AddAmmo(ammoAmount);
            
            // Notify audio announcer
            AIAudioAnnouncer audioAnnouncer = teammate.GetComponent<AIAudioAnnouncer>();
            if (audioAnnouncer != null)
            {
                audioAnnouncer.PlayLootFound();
            }
            
            Debug.Log($"Teammate {teammate.name} picked up {weaponType} with {ammoAmount} ammo");
        }
        
        Pickup();
    }
    
    void Pickup()
    {
        if (isPickedUp) return;
        
        isPickedUp = true;
        
        // Play pickup sound
        if (pickupSound != null)
        {
            AudioSource.PlayClipAtPoint(pickupSound, transform.position, pickupVolume);
        }
        
        // Spawn pickup effect
        if (pickupEffect != null)
        {
            GameObject effect = Instantiate(pickupEffect, transform.position, Quaternion.identity);
            Destroy(effect, 3f);
        }
        
        // Destroy the weapon pickup
        Destroy(gameObject);
    }
    
    void OnTriggerEnter(Collider other)
    {
        if (isPickedUp) return;
        
        // Check if player entered
        PlayerController player = other.GetComponent<PlayerController>();
        if (player != null)
        {
            PickupByPlayer(player);
            return;
        }
        
        // Check if ML-Agent entered
        SquadMateAgent agent = other.GetComponent<SquadMateAgent>();
        if (agent != null)
        {
            PickupByAgent(agent);
            return;
        }
        
        // Check if AI teammate entered
        TeammateAI teammate = other.GetComponent<TeammateAI>();
        if (teammate != null)
        {
            PickupByTeammate(teammate);
            return;
        }
    }
    
    // Static method to create weapon pickups
    public static GameObject CreateWeaponPickup(WeaponSystem.WeaponType type, Vector3 position, int ammo = 30)
    {
        GameObject weaponObj = null;
        
        // Create appropriate primitive based on weapon type
        switch (type)
        {
            case WeaponSystem.WeaponType.Pistol:
                weaponObj = GameObject.CreatePrimitive(PrimitiveType.Capsule);
                weaponObj.transform.localScale = new Vector3(0.3f, 0.8f, 0.3f);
                break;
            case WeaponSystem.WeaponType.AssaultRifle:
                weaponObj = GameObject.CreatePrimitive(PrimitiveType.Capsule);
                weaponObj.transform.localScale = new Vector3(0.4f, 1.5f, 0.4f);
                break;
            case WeaponSystem.WeaponType.SniperRifle:
                weaponObj = GameObject.CreatePrimitive(PrimitiveType.Capsule);
                weaponObj.transform.localScale = new Vector3(0.3f, 2f, 0.3f);
                break;
            default:
                weaponObj = GameObject.CreatePrimitive(PrimitiveType.Cube);
                break;
        }
        
        weaponObj.transform.position = position;
        weaponObj.name = $"WeaponPickup_{type}_{Random.Range(1000, 9999)}";
        
        WeaponPickup pickup = weaponObj.AddComponent<WeaponPickup>();
        pickup.weaponType = type;
        pickup.ammoAmount = ammo;
        
        // Set color based on weapon type
        Renderer renderer = weaponObj.GetComponent<Renderer>();
        if (renderer != null)
        {
            switch (type)
            {
                case WeaponSystem.WeaponType.Pistol:
                    renderer.material.color = Color.gray;
                    pickup.glowColor = Color.gray;
                    break;
                case WeaponSystem.WeaponType.AssaultRifle:
                    renderer.material.color = Color.black;
                    pickup.glowColor = Color.yellow;
                    break;
                case WeaponSystem.WeaponType.SniperRifle:
                    renderer.material.color = Color.blue;
                    pickup.glowColor = Color.blue;
                    break;
            }
        }
        
        return weaponObj;
    }
    
    void OnDrawGizmosSelected()
    {
        // Draw pickup range
        Gizmos.color = Color.green;
        Gizmos.DrawWireSphere(transform.position, pickupRange);
    }
}
