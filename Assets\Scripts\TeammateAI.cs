using UnityEngine;
using UnityEngine.AI;
using System.Collections.Generic;
using System.Collections;

public class TeammateAI : MonoBehaviour
{
    [Header("AI Settings")]
    public float followDistance = 4f;
    public float maxFollowDistance = 12f;
    public float reviveDistance = 2f;
    public float reviveTime = 3f;
    public float attackRange = 20f;
    public float lootRange = 6f;

    [Header("Combat Settings")]
    public float damage = 35f;
    public float fireRate = 3f;
    public float accuracy = 0.85f;
    public LayerMask enemyLayer = 1 << 8;
    public LayerMask lootLayer = 1 << 9;
    public LayerMask coverLayer = 1 << 10;

    [Header("Professional Behavior")]
    public float threatAssessmentRange = 25f;
    public float coverSeekDistance = 8f;
    public float flankingDistance = 10f;
    public Transform[] coverPoints;
    public AudioClip[] voiceLines;

    [Header("References")]
    public PlayerController player;
    public Transform firePoint;
    public GameObject bulletPrefab;
    public GameObject muzzleFlash;

    private NavMeshAgent agent;
    public AIState currentState = AIState.Following;
    private TacticalState tacticalState = TacticalState.Neutral;
    private float lastFireTime;
    public float reviveProgress;
    private Transform currentTarget;
    private Transform currentCover;
    private List<Transform> nearbyLoot = new List<Transform>();
    private List<Transform> threatsInRange = new List<Transform>();
    private AudioSource audioSource;
    private float lastVoiceLineTime;
    private float voiceLineCooldown = 5f;

    // New system integrations
    private AIAudioAnnouncer audioAnnouncer;
    private SquadMemberCoordinator squadCoordinator;

    public enum AIState
    {
        Following,
        Reviving,
        Combat,
        TakingCover,
        Flanking,
        Looting,
        Idle
    }

    public enum TacticalState
    {
        Neutral,
        Defensive,
        Aggressive,
        Retreating,
        Supporting
    }
    
    void Start()
    {
        agent = GetComponent<NavMeshAgent>();
        if (agent == null)
        {
            agent = gameObject.AddComponent<NavMeshAgent>();
        }

        // Configure NavMeshAgent for professional behavior
        agent.speed = 4.5f;
        agent.acceleration = 12f;
        agent.angularSpeed = 180f;
        agent.stoppingDistance = 1.5f;
        agent.obstacleAvoidanceType = ObstacleAvoidanceType.HighQualityObstacleAvoidance;

        // Get audio source
        audioSource = GetComponent<AudioSource>();
        if (audioSource == null)
        {
            audioSource = gameObject.AddComponent<AudioSource>();
        }

        // Initialize new system components
        audioAnnouncer = GetComponent<AIAudioAnnouncer>();
        squadCoordinator = GetComponent<SquadMemberCoordinator>();

        // Find player if not assigned
        if (player == null)
        {
            player = FindObjectOfType<PlayerController>();
        }

        // Subscribe to player events
        if (player != null)
        {
            player.OnPlayerDownedChanged += OnPlayerDownedChanged;
            player.OnPlayerMoved += OnPlayerMoved;
        }

        // Set up fire point if not assigned
        if (firePoint == null)
        {
            GameObject firePointObj = new GameObject("FirePoint");
            firePointObj.transform.SetParent(transform);
            firePointObj.transform.localPosition = Vector3.forward + Vector3.up;
            firePoint = firePointObj.transform;
        }

        // Play join voice line
        PlayVoiceLine("Teammate reporting for duty!");

        InvokeRepeating(nameof(UpdateAI), 0f, 0.08f); // Update AI 12.5 times per second for responsiveness
        InvokeRepeating(nameof(ThreatAssessment), 0f, 0.2f); // Threat assessment 5 times per second
    }
    
    void UpdateAI()
    {
        if (player == null) return;
        
        // Check for state transitions
        CheckStateTransitions();
        
        // Execute current state behavior
        switch (currentState)
        {
            case AIState.Following:
                FollowPlayer();
                break;
            case AIState.Reviving:
                RevivePlayer();
                break;
            case AIState.Combat:
                CombatBehavior();
                break;
            case AIState.TakingCover:
                TakeCoverBehavior();
                break;
            case AIState.Flanking:
                FlankingBehavior();
                break;
            case AIState.Looting:
                LootBehavior();
                break;
            case AIState.Idle:
                IdleBehavior();
                break;
        }
    }
    
    void CheckStateTransitions()
    {
        float distanceToPlayer = Vector3.Distance(transform.position, player.transform.position);
        ReviveSystem reviveSystem = player.GetReviveSystem();

        // Professional AI Decision Tree

        // CRITICAL PRIORITY: Player needs immediate revive and area is safe
        if (reviveSystem.NeedsRevive() && distanceToPlayer <= reviveDistance && threatsInRange.Count == 0)
        {
            ChangeState(AIState.Reviving);
            SetTacticalState(TacticalState.Supporting);

            // Notify squad coordinator
            if (squadCoordinator != null)
            {
                squadCoordinator.StartSpecialAction("reviving_player");
            }

            // Play audio
            if (audioAnnouncer != null)
            {
                audioAnnouncer.PlayRevivingPlayer();
            }

            return;
        }

        // HIGH PRIORITY: Multiple enemies - take cover or flank
        if (threatsInRange.Count >= 2)
        {
            if (ShouldTakeCover())
            {
                ChangeState(AIState.TakingCover);
                SetTacticalState(TacticalState.Defensive);
                return;
            }
            else if (CanFlankEnemies())
            {
                ChangeState(AIState.Flanking);
                SetTacticalState(TacticalState.Aggressive);
                return;
            }
        }

        // MEDIUM PRIORITY: Single enemy engagement
        Transform nearestEnemy = FindNearestEnemy();
        if (nearestEnemy != null)
        {
            currentTarget = nearestEnemy;
            ChangeState(AIState.Combat);
            SetTacticalState(TacticalState.Aggressive);

            // Notify squad coordinator
            if (squadCoordinator != null)
            {
                squadCoordinator.StartSpecialAction("engaging_enemy");
            }

            // Play audio
            if (audioAnnouncer != null)
            {
                audioAnnouncer.PlayEnemyDetected();
            }

            return;
        }

        // MEDIUM PRIORITY: Player downed but enemies present - clear area first
        if (reviveSystem.NeedsRevive() && threatsInRange.Count > 0)
        {
            ChangeState(AIState.Combat);
            SetTacticalState(TacticalState.Supporting);
            PlayVoiceLine("Clearing enemies before revive!");
            return;
        }

        // MEDIUM PRIORITY: Move to revive downed player
        if (reviveSystem.NeedsRevive())
        {
            ChangeState(AIState.Following);
            SetTacticalState(TacticalState.Supporting);
            return;
        }

        // LOW PRIORITY: Loot collection when safe
        if (FindNearbyLoot().Count > 0 && distanceToPlayer <= maxFollowDistance && threatsInRange.Count == 0)
        {
            ChangeState(AIState.Looting);
            SetTacticalState(TacticalState.Neutral);
            return;
        }

        // LOW PRIORITY: Follow player if too far
        if (distanceToPlayer > followDistance)
        {
            ChangeState(AIState.Following);
            SetTacticalState(TacticalState.Neutral);
            return;
        }

        // DEFAULT: Idle but alert
        ChangeState(AIState.Idle);
        SetTacticalState(TacticalState.Neutral);
    }
    
    void ChangeState(AIState newState)
    {
        if (currentState == newState) return;
        
        // Exit current state
        switch (currentState)
        {
            case AIState.Reviving:
                reviveProgress = 0f;
                break;
        }
        
        currentState = newState;
        Debug.Log($"AI State changed to: {newState}");
    }
    
    void FollowPlayer()
    {
        if (player == null) return;
        
        Vector3 targetPosition = player.transform.position;
        float distance = Vector3.Distance(transform.position, targetPosition);
        
        if (distance > followDistance)
        {
            if (IsAgentValid()) agent.SetDestination(targetPosition);
        }
        else
        {
            if (IsAgentValid()) agent.ResetPath();
        }
    }
    
    void RevivePlayer()
    {
        ReviveSystem reviveSystem = player.GetReviveSystem();
        if (!reviveSystem.NeedsRevive()) return;

        // Move to player if not close enough
        float distance = Vector3.Distance(transform.position, player.transform.position);
        if (distance > reviveDistance)
        {
            if (IsAgentValid()) agent.SetDestination(player.transform.position);
            return;
        }

        // Stop moving and start reviving
        if (IsAgentValid()) agent.ResetPath();

        // Look at player
        Vector3 lookDirection = (player.transform.position - transform.position).normalized;
        if (lookDirection != Vector3.zero)
        {
            transform.rotation = Quaternion.LookRotation(lookDirection);
        }

        // Revive progress
        reviveProgress += Time.deltaTime;

        if (reviveProgress >= reviveTime)
        {
            player.Revive();
            reviveProgress = 0f;

            // Notify squad coordinator
            if (squadCoordinator != null)
            {
                squadCoordinator.EndSpecialAction();
            }

            // Play audio
            if (audioAnnouncer != null)
            {
                audioAnnouncer.PlayPlayerRevived();
            }
        }
    }
    
    void CombatBehavior()
    {
        if (currentTarget == null)
        {
            currentTarget = FindNearestEnemy();
            if (currentTarget == null) return;
        }
        
        float distanceToTarget = Vector3.Distance(transform.position, currentTarget.position);
        
        // Move closer if too far
        if (distanceToTarget > attackRange)
        {
            if (IsAgentValid()) agent.SetDestination(currentTarget.position);
        }
        else
        {
            // Stop and shoot
            if (IsAgentValid()) agent.ResetPath();
            
            // Look at target
            Vector3 lookDirection = (currentTarget.position - transform.position).normalized;
            if (lookDirection != Vector3.zero)
            {
                transform.rotation = Quaternion.LookRotation(lookDirection);
            }
            
            // Shoot
            if (Time.time >= lastFireTime + (1f / fireRate))
            {
                Shoot(currentTarget);
                lastFireTime = Time.time;
            }
        }
    }
    
    void LootBehavior()
    {
        List<Transform> loot = FindNearbyLoot();
        if (loot.Count == 0) return;
        
        Transform nearestLoot = loot[0];
        float nearestDistance = Vector3.Distance(transform.position, nearestLoot.position);
        
        foreach (Transform lootItem in loot)
        {
            float distance = Vector3.Distance(transform.position, lootItem.position);
            if (distance < nearestDistance)
            {
                nearestLoot = lootItem;
                nearestDistance = distance;
            }
        }
        
        if (nearestDistance <= 1f)
        {
            // Collect loot
            CollectLoot(nearestLoot);
        }
        else
        {
            if (IsAgentValid()) agent.SetDestination(nearestLoot.position);
        }
    }
    
    void IdleBehavior()
    {
        if (IsAgentValid()) agent.ResetPath();
        
        // Look around occasionally
        if (Random.Range(0f, 1f) < 0.01f) // 1% chance per frame
        {
            float randomAngle = Random.Range(0f, 360f);
            transform.rotation = Quaternion.Euler(0f, randomAngle, 0f);
        }
    }
    
    Transform FindNearestEnemy()
    {
        Collider[] enemies = Physics.OverlapSphere(transform.position, attackRange, enemyLayer);
        Transform nearest = null;
        float nearestDistance = float.MaxValue;
        
        foreach (Collider enemy in enemies)
        {
            float distance = Vector3.Distance(transform.position, enemy.transform.position);
            if (distance < nearestDistance)
            {
                nearest = enemy.transform;
                nearestDistance = distance;
            }
        }
        
        return nearest;
    }
    
    List<Transform> FindNearbyLoot()
    {
        nearbyLoot.Clear();
        Collider[] lootItems = Physics.OverlapSphere(transform.position, lootRange, lootLayer);
        
        foreach (Collider loot in lootItems)
        {
            nearbyLoot.Add(loot.transform);
        }
        
        return nearbyLoot;
    }
    
    void Shoot(Transform target)
    {
        EnhancedShoot(target);
    }
    
    void CollectLoot(Transform lootItem)
    {
        // Check if it's a LootItem component
        LootItem lootComponent = lootItem.GetComponent<LootItem>();
        if (lootComponent != null)
        {
            lootComponent.CollectByTeammate(this);
        }
        else
        {
            // Fallback for old loot system
            Debug.Log($"AI collected loot: {lootItem.name}");

            // Notify audio announcer
            if (audioAnnouncer != null)
            {
                audioAnnouncer.PlayLootFound();
            }

            Destroy(lootItem.gameObject);
        }

        // Notify squad coordinator
        if (squadCoordinator != null)
        {
            squadCoordinator.StartSpecialAction("collecting_loot");
        }
    }
    
    void OnPlayerDownedChanged(bool isDowned)
    {
        if (isDowned)
        {
            Debug.Log("Player is down! AI rushing to revive.");
        }
    }
    
    void OnPlayerMoved(Vector3 newPosition)
    {
        // Player moved, AI might need to follow
    }
    
    void OnDestroy()
    {
        if (player != null)
        {
            player.OnPlayerDownedChanged -= OnPlayerDownedChanged;
            player.OnPlayerMoved -= OnPlayerMoved;
        }
    }
    
    // Professional AI Methods

    void ThreatAssessment()
    {
        threatsInRange.Clear();
        Collider[] enemies = Physics.OverlapSphere(transform.position, threatAssessmentRange, enemyLayer);

        foreach (Collider enemy in enemies)
        {
            if (enemy.transform != null)
            {
                threatsInRange.Add(enemy.transform);
            }
        }
    }

    void SetTacticalState(TacticalState newState)
    {
        if (tacticalState != newState)
        {
            tacticalState = newState;
            Debug.Log($"AI Tactical State: {newState}");
        }
    }

    bool ShouldTakeCover()
    {
        // Take cover if outnumbered or player is down
        return threatsInRange.Count >= 2 || (player != null && player.IsPlayerDowned());
    }

    bool CanFlankEnemies()
    {
        // Can flank if there's a clear path and not protecting downed player
        return threatsInRange.Count <= 2 && (player == null || !player.IsPlayerDowned());
    }

    void TakeCoverBehavior()
    {
        if (currentCover == null)
        {
            currentCover = FindNearestCover();
        }

        if (currentCover != null)
        {
            float distanceToCover = Vector3.Distance(transform.position, currentCover.position);

            if (distanceToCover > 1f)
            {
                agent.SetDestination(currentCover.position);
            }
            else
            {
                agent.ResetPath();
                // Engage enemies from cover
                Transform nearestThreat = FindNearestThreat();
                if (nearestThreat != null)
                {
                    LookAtTarget(nearestThreat);
                    if (CanShoot(nearestThreat))
                    {
                        Shoot(nearestThreat);

                        // Play attacking audio
                        if (audioAnnouncer != null && Random.Range(0f, 1f) < 0.1f) // 10% chance
                        {
                            audioAnnouncer.PlayAttacking();
                        }
                    }
                }
            }
        }
        else
        {
            // No cover available, fall back to combat
            ChangeState(AIState.Combat);
        }
    }

    void FlankingBehavior()
    {
        Transform primaryThreat = FindNearestThreat();
        if (primaryThreat == null)
        {
            ChangeState(AIState.Following);
            return;
        }

        Vector3 flankPosition = CalculateFlankPosition(primaryThreat);
        float distanceToFlank = Vector3.Distance(transform.position, flankPosition);

        if (distanceToFlank > 2f)
        {
            agent.SetDestination(flankPosition);
        }
        else
        {
            agent.ResetPath();
            LookAtTarget(primaryThreat);
            if (CanShoot(primaryThreat))
            {
                Shoot(primaryThreat);
                PlayVoiceLine("Flanking maneuver!");
            }
        }
    }

    Transform FindNearestCover()
    {
        if (coverPoints == null || coverPoints.Length == 0)
        {
            // Find cover dynamically
            Collider[] covers = Physics.OverlapSphere(transform.position, coverSeekDistance, coverLayer);
            if (covers.Length > 0)
            {
                return covers[0].transform;
            }
            return null;
        }

        Transform nearestCover = null;
        float nearestDistance = float.MaxValue;

        foreach (Transform cover in coverPoints)
        {
            if (cover == null) continue;

            float distance = Vector3.Distance(transform.position, cover.position);
            if (distance < nearestDistance)
            {
                nearestCover = cover;
                nearestDistance = distance;
            }
        }

        return nearestCover;
    }

    Transform FindNearestThreat()
    {
        if (threatsInRange.Count == 0) return null;

        Transform nearestThreat = null;
        float nearestDistance = float.MaxValue;

        foreach (Transform threat in threatsInRange)
        {
            if (threat == null) continue;

            float distance = Vector3.Distance(transform.position, threat.position);
            if (distance < nearestDistance)
            {
                nearestThreat = threat;
                nearestDistance = distance;
            }
        }

        return nearestThreat;
    }

    Vector3 CalculateFlankPosition(Transform target)
    {
        Vector3 toTarget = (target.position - transform.position).normalized;
        Vector3 rightFlank = Vector3.Cross(toTarget, Vector3.up) * flankingDistance;
        Vector3 leftFlank = -rightFlank;

        // Choose the flank position that's further from player (to avoid crossfire)
        Vector3 playerPos = player != null ? player.transform.position : transform.position;
        Vector3 rightPos = target.position + rightFlank;
        Vector3 leftPos = target.position + leftFlank;

        float rightDist = Vector3.Distance(rightPos, playerPos);
        float leftDist = Vector3.Distance(leftPos, playerPos);

        return rightDist > leftDist ? rightPos : leftPos;
    }

    bool CanShoot(Transform target)
    {
        if (target == null || firePoint == null) return false;

        // Check line of sight
        Vector3 direction = (target.position - firePoint.position).normalized;
        RaycastHit hit;

        if (Physics.Raycast(firePoint.position, direction, out hit, attackRange))
        {
            return hit.collider.transform == target;
        }

        return false;
    }

    void LookAtTarget(Transform target)
    {
        if (target == null) return;

        Vector3 direction = (target.position - transform.position).normalized;
        direction.y = 0; // Keep horizontal

        if (direction != Vector3.zero)
        {
            Quaternion targetRotation = Quaternion.LookRotation(direction);
            transform.rotation = Quaternion.Slerp(transform.rotation, targetRotation, Time.deltaTime * 5f);
        }
    }

    void PlayVoiceLine(string message)
    {
        if (Time.time < lastVoiceLineTime + voiceLineCooldown) return;

        Debug.Log($"AI: {message}");

        if (voiceLines != null && voiceLines.Length > 0 && audioSource != null)
        {
            AudioClip randomLine = voiceLines[Random.Range(0, voiceLines.Length)];
            audioSource.PlayOneShot(randomLine);
        }

        lastVoiceLineTime = Time.time;
    }

    // Enhanced combat behavior
    void EnhancedShoot(Transform target)
    {
        if (target == null || firePoint == null) return;

        // Apply accuracy
        Vector3 targetPos = target.position;
        if (accuracy < 1f)
        {
            float spread = (1f - accuracy) * 2f;
            targetPos += new Vector3(
                Random.Range(-spread, spread),
                Random.Range(-spread, spread),
                Random.Range(-spread, spread)
            );
        }

        // Muzzle flash effect
        if (muzzleFlash != null)
        {
            StartCoroutine(ShowMuzzleFlash());
        }

        // Raycast damage
        Vector3 direction = (targetPos - firePoint.position).normalized;
        RaycastHit hit;

        if (Physics.Raycast(firePoint.position, direction, out hit, attackRange))
        {
            // Check for EnemyAI component
            EnemyAI enemy = hit.collider.GetComponent<EnemyAI>();
            if (enemy != null)
            {
                enemy.TakeDamage(damage);
                PlayVoiceLine("Target hit!");
                return;
            }

            // Check for EnemyHealth component
            EnemyHealth enemyHealth = hit.collider.GetComponent<EnemyHealth>();
            if (enemyHealth != null)
            {
                enemyHealth.TakeDamage(damage);
                PlayVoiceLine("Target hit!");

                // Check if enemy was killed
                if (enemyHealth.IsDead() && audioAnnouncer != null)
                {
                    audioAnnouncer.PlayEnemyKilled();
                }
            }
        }
    }

    IEnumerator ShowMuzzleFlash()
    {
        if (muzzleFlash != null)
        {
            muzzleFlash.SetActive(true);
            yield return new WaitForSeconds(0.1f);
            muzzleFlash.SetActive(false);
        }
    }

    // Public method for party invites (enhanced)
    public void ReceivePartyInvite(string playerName)
    {
        PlayVoiceLine($"Invitation accepted from {playerName}!");
        Debug.Log($"AI received party invite from {playerName}. Accepting!");
    }

    // Helper method to validate NavMeshAgent state
    bool IsAgentValid()
    {
        return agent != null && agent.enabled && agent.isOnNavMesh && agent.isActiveAndEnabled;
    }

    void OnDrawGizmosSelected()
    {
        // Draw threat assessment range
        Gizmos.color = Color.red;
        Gizmos.DrawWireSphere(transform.position, threatAssessmentRange);

        // Draw attack range
        Gizmos.color = Color.orange;
        Gizmos.DrawWireSphere(transform.position, attackRange);

        // Draw follow distance
        Gizmos.color = Color.green;
        Gizmos.DrawWireSphere(transform.position, followDistance);
    }
}
