using UnityEngine;
using UnityEngine.AI;

/// <summary>
/// Visual status indicator for NavMesh system
/// Shows clear status and provides one-click setup
/// </summary>
public class NavMeshStatusUI : MonoBehaviour
{
    [Header("UI Settings")]
    public bool showStatusUI = true;
    public bool showInstructions = true;
    public float uiScale = 1f;
    
    private bool navMeshExists = false;
    private int validAgents = 0;
    private int totalAgents = 0;
    private string statusMessage = "";
    
    void Update()
    {
        UpdateNavMeshStatus();
    }
    
    void UpdateNavMeshStatus()
    {
        // Check NavMesh existence
        NavMeshTriangulation triangulation = NavMesh.CalculateTriangulation();
        navMeshExists = triangulation.vertices.Length > 0;
        
        // Count valid agents
        NavMeshAgent[] agents = FindObjectsOfType<NavMeshAgent>();
        totalAgents = agents.Length;
        validAgents = 0;
        
        foreach (NavMeshAgent agent in agents)
        {
            if (NavMeshValidator.IsAgentValid(agent))
            {
                validAgents++;
            }
        }
        
        // Update status message
        if (!navMeshExists)
        {
            statusMessage = "NavMesh not found - Setup required";
        }
        else if (validAgents == totalAgents && totalAgents > 0)
        {
            statusMessage = "All systems operational";
        }
        else if (totalAgents == 0)
        {
            statusMessage = "NavMesh ready - No agents in scene";
        }
        else
        {
            statusMessage = $"NavMesh ready - {validAgents}/{totalAgents} agents valid";
        }
    }
    
    void OnGUI()
    {
        if (!showStatusUI) return;
        
        // Scale UI
        Matrix4x4 oldMatrix = GUI.matrix;
        GUI.matrix = Matrix4x4.Scale(Vector3.one * uiScale);
        
        // Status panel
        DrawStatusPanel();
        
        // Instructions panel (only if NavMesh missing)
        if (!navMeshExists && showInstructions)
        {
            DrawInstructionsPanel();
        }
        
        // Restore UI matrix
        GUI.matrix = oldMatrix;
    }
    
    void DrawStatusPanel()
    {
        // Background
        GUI.Box(new Rect(10, 10, 300, 80), "");
        
        // Title
        GUI.Label(new Rect(20, 20, 280, 20), "Unity 6 AI Teammate Bot - NavMesh Status");
        
        // Status indicator
        if (navMeshExists)
        {
            GUI.color = Color.green;
            GUI.Label(new Rect(20, 40, 20, 20), "✅");
            GUI.color = Color.white;
            GUI.Label(new Rect(45, 40, 250, 20), "NavMesh: Ready");
        }
        else
        {
            GUI.color = Color.red;
            GUI.Label(new Rect(20, 40, 20, 20), "❌");
            GUI.color = Color.white;
            GUI.Label(new Rect(45, 40, 250, 20), "NavMesh: Not Found");
        }
        
        // Agent status
        if (totalAgents > 0)
        {
            if (validAgents == totalAgents)
            {
                GUI.color = Color.green;
                GUI.Label(new Rect(20, 60, 20, 20), "✅");
            }
            else
            {
                GUI.color = Color.yellow;
                GUI.Label(new Rect(20, 60, 20, 20), "⚠️");
            }
            GUI.color = Color.white;
            GUI.Label(new Rect(45, 60, 250, 20), $"Agents: {validAgents}/{totalAgents} valid");
        }
        else
        {
            GUI.color = Color.gray;
            GUI.Label(new Rect(20, 60, 20, 20), "ℹ️");
            GUI.color = Color.white;
            GUI.Label(new Rect(45, 60, 250, 20), "Agents: None in scene");
        }
        
        GUI.color = Color.white;
    }
    
    void DrawInstructionsPanel()
    {
        // Background
        GUI.color = new Color(1f, 1f, 0f, 0.8f); // Yellow background
        GUI.Box(new Rect(10, 100, 400, 200), "");
        GUI.color = Color.white;
        
        // Title
        GUI.Label(new Rect(20, 110, 380, 20), "🚀 NavMesh Setup Required");
        
        // Instructions
        GUI.Label(new Rect(20, 135, 380, 20), "Choose one of these options:");
        
        // Option 1: Auto setup button
        GUI.color = Color.green;
        if (GUI.Button(new Rect(20, 160, 180, 30), "🔥 Auto Setup NavMesh (F8)"))
        {
            AutoSetupNavMesh();
        }
        GUI.color = Color.white;
        
        // Option 2: Manual instructions
        GUI.Label(new Rect(20, 200, 380, 20), "OR Manual Setup:");
        GUI.Label(new Rect(20, 220, 380, 20), "1. Window > AI > Navigation");
        GUI.Label(new Rect(20, 240, 380, 20), "2. Select ground objects");
        GUI.Label(new Rect(20, 260, 380, 20), "3. Mark as 'Navigation Static'");
        GUI.Label(new Rect(20, 280, 380, 20), "4. Click 'Bake' button");
        
        // Hotkeys
        GUI.color = Color.cyan;
        GUI.Label(new Rect(220, 160, 180, 30), "Hotkey: F8");
        GUI.color = Color.white;
    }
    
    void AutoSetupNavMesh()
    {
        // Find or create AutoNavMeshSetup
        AutoNavMeshSetup autoSetup = FindObjectOfType<AutoNavMeshSetup>();
        
        if (autoSetup == null)
        {
            GameObject setupObj = new GameObject("AutoNavMeshSetup");
            autoSetup = setupObj.AddComponent<AutoNavMeshSetup>();
        }
        
        // Trigger setup
        autoSetup.SetupNavMeshNow();
        
        Debug.Log("🚀 Auto NavMesh setup triggered!");
    }
    
    void Update()
    {
        UpdateNavMeshStatus();
        
        // Hotkey for auto setup
        if (Input.GetKeyDown(KeyCode.F8))
        {
            AutoSetupNavMesh();
        }
        
        // Toggle UI
        if (Input.GetKeyDown(KeyCode.F7))
        {
            showStatusUI = !showStatusUI;
        }
    }
    
    // Public methods for external control
    public void SetUIVisible(bool visible)
    {
        showStatusUI = visible;
    }
    
    public void SetInstructionsVisible(bool visible)
    {
        showInstructions = visible;
    }
    
    public bool IsNavMeshReady()
    {
        return navMeshExists;
    }
    
    public bool AreAllAgentsValid()
    {
        return totalAgents > 0 && validAgents == totalAgents;
    }
    
    public string GetStatusMessage()
    {
        return statusMessage;
    }
}
