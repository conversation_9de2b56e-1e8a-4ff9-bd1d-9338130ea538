using UnityEngine;

/// <summary>
/// Individual squad member coordination component
/// Handles communication with SquadManager and prevents overlapping actions
/// </summary>
public class SquadMemberCoordinator : MonoBehaviour
{
    [Header("Squad Info")]
    public int memberNumber;
    public string squadRole = "Assault";
    
    [Header("Coordination Settings")]
    public float avoidanceRadius = 3f;
    public bool preventOverlappingActions = true;
    public float actionCooldown = 1f;
    
    private SquadManager squadManager;
    private TeammateAI teammateAI;
    private UnityEngine.AI.NavMeshAgent agent;
    private float lastActionTime = 0f;
    private bool isPerformingSpecialAction = false;
    
    public enum SquadRole
    {
        Assault,    // Primary combat role
        Support,    // Healing and support role
        Scout,      // Reconnaissance role
        Heavy       // Tank/heavy weapons role
    }
    
    void Start()
    {
        teammateAI = GetComponent<TeammateAI>();
        agent = GetComponent<UnityEngine.AI.NavMeshAgent>();
    }
    
    void Update()
    {
        if (squadManager != null && preventOverlappingActions)
        {
            CoordinateWithSquad();
        }
    }
    
    public void Initialize(SquadManager manager, int number)
    {
        squadManager = manager;
        memberNumber = number;
        
        // Assign role based on member number
        AssignRole();
        
        Debug.Log($"Squad member {memberNumber} initialized with role: {squadRole}");
    }
    
    void AssignRole()
    {
        // Simple role assignment based on member number
        switch (memberNumber % 4)
        {
            case 1:
                squadRole = "Assault";
                break;
            case 2:
                squadRole = "Support";
                break;
            case 3:
                squadRole = "Scout";
                break;
            case 0:
                squadRole = "Heavy";
                break;
        }
    }
    
    void CoordinateWithSquad()
    {
        if (squadManager == null) return;
        
        // Avoid clustering with other squad members
        AvoidSquadMemberClustering();
        
        // Check for formation position
        Vector3 formationPos = squadManager.GetFormationPosition(teammateAI);
        if (formationPos != Vector3.zero && !isPerformingSpecialAction)
        {
            MaintainFormation(formationPos);
        }
    }
    
    void AvoidSquadMemberClustering()
    {
        if (agent == null || !agent.isOnNavMesh) return;
        
        var squadMembers = squadManager.GetSquadMembers();
        
        foreach (TeammateAI otherMember in squadMembers)
        {
            if (otherMember == null || otherMember == teammateAI) continue;
            
            float distance = Vector3.Distance(transform.position, otherMember.transform.position);
            
            if (distance < avoidanceRadius)
            {
                // Calculate avoidance direction
                Vector3 avoidDirection = (transform.position - otherMember.transform.position).normalized;
                Vector3 avoidPosition = transform.position + avoidDirection * avoidanceRadius;
                
                // Sample a valid NavMesh position
                UnityEngine.AI.NavMeshHit hit;
                if (UnityEngine.AI.NavMesh.SamplePosition(avoidPosition, out hit, avoidanceRadius, UnityEngine.AI.NavMesh.AllAreas))
                {
                    if (agent.isActiveAndEnabled && !isPerformingSpecialAction)
                    {
                        agent.SetDestination(hit.position);
                    }
                }
            }
        }
    }
    
    void MaintainFormation(Vector3 formationPosition)
    {
        if (agent == null || !agent.isOnNavMesh) return;
        
        float distanceToFormation = Vector3.Distance(transform.position, formationPosition);
        
        // Only move to formation if we're far from it and not doing something important
        if (distanceToFormation > 2f && !isPerformingSpecialAction)
        {
            agent.SetDestination(formationPosition);
        }
    }
    
    // Called by TeammateAI when starting important actions
    public void StartSpecialAction(string actionName)
    {
        if (Time.time - lastActionTime < actionCooldown) return;
        
        isPerformingSpecialAction = true;
        lastActionTime = Time.time;
        
        Debug.Log($"Squad member {memberNumber} starting special action: {actionName}");
        
        // Notify other squad members about this action
        NotifySquadOfAction(actionName);
    }
    
    public void EndSpecialAction()
    {
        isPerformingSpecialAction = false;
        Debug.Log($"Squad member {memberNumber} finished special action");
    }
    
    void NotifySquadOfAction(string actionName)
    {
        if (squadManager == null) return;
        
        var squadMembers = squadManager.GetSquadMembers();
        
        foreach (TeammateAI member in squadMembers)
        {
            if (member == null || member == teammateAI) continue;
            
            SquadMemberCoordinator otherCoordinator = member.GetComponent<SquadMemberCoordinator>();
            if (otherCoordinator != null)
            {
                otherCoordinator.OnSquadMemberAction(memberNumber, actionName);
            }
        }
    }
    
    public void OnSquadMemberAction(int memberNum, string actionName)
    {
        // React to other squad member actions
        switch (actionName)
        {
            case "reviving_player":
                // Don't also try to revive - provide cover instead
                if (!isPerformingSpecialAction)
                {
                    StartSpecialAction("providing_cover");
                }
                break;
                
            case "engaging_enemy":
                // Coordinate combat - don't all attack the same target
                break;
                
            case "collecting_loot":
                // Let them collect, find other loot or maintain position
                break;
        }
    }
    
    // Role-specific behavior modifiers
    public float GetRoleSpeedModifier()
    {
        switch (squadRole)
        {
            case "Scout":
                return 1.2f; // Scouts move faster
            case "Heavy":
                return 0.8f; // Heavy units move slower
            default:
                return 1f;
        }
    }
    
    public float GetRoleCombatModifier()
    {
        switch (squadRole)
        {
            case "Assault":
                return 1.2f; // More aggressive
            case "Support":
                return 0.8f; // Less aggressive, focus on support
            case "Heavy":
                return 1.5f; // High damage output
            case "Scout":
                return 1f;   // Balanced
            default:
                return 1f;
        }
    }
    
    public bool ShouldPrioritizeAction(string actionType)
    {
        switch (squadRole)
        {
            case "Support":
                return actionType == "reviving_player" || actionType == "collecting_medkit";
            case "Scout":
                return actionType == "investigating" || actionType == "collecting_loot";
            case "Assault":
                return actionType == "engaging_enemy" || actionType == "eliminating_threats";
            case "Heavy":
                return actionType == "engaging_enemy" || actionType == "providing_cover";
            default:
                return false;
        }
    }
    
    // Public getters
    public bool IsPerformingSpecialAction()
    {
        return isPerformingSpecialAction;
    }
    
    public string GetSquadRole()
    {
        return squadRole;
    }
    
    public int GetMemberNumber()
    {
        return memberNumber;
    }
    
    public SquadManager GetSquadManager()
    {
        return squadManager;
    }
    
    void OnDrawGizmosSelected()
    {
        // Draw avoidance radius
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(transform.position, avoidanceRadius);
        
        // Draw formation position if available
        if (squadManager != null && teammateAI != null)
        {
            Vector3 formationPos = squadManager.GetFormationPosition(teammateAI);
            if (formationPos != Vector3.zero)
            {
                Gizmos.color = Color.blue;
                Gizmos.DrawLine(transform.position, formationPos);
                Gizmos.DrawWireSphere(formationPos, 0.5f);
            }
        }
    }
}
