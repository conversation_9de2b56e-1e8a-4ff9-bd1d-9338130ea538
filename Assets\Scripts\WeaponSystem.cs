using UnityEngine;

/// <summary>
/// Weapon system for ML-Agent and player combat
/// Handles shooting, damage, and weapon management
/// </summary>
public class WeaponSystem : MonoBehaviour
{
    [Header("Weapon Settings")]
    public WeaponType currentWeapon = WeaponType.None;
    public float damage = 25f;
    public float range = 20f;
    public float fireRate = 2f; // Shots per second
    public int maxAmmo = 30;
    public int currentAmmo = 30;
    
    [Header("Audio")]
    public AudioClip fireSound;
    public AudioClip reloadSound;
    public AudioClip emptySound;
    
    [Header("Effects")]
    public GameObject muzzleFlash;
    public GameObject impactEffect;
    public Transform firePoint;
    
    [Header("Weapon Models")]
    public GameObject assaultRifleModel;
    public GameObject pistolModel;
    public GameObject sniperRifleModel;
    
    private AudioSource audioSource;
    private float lastFireTime = 0f;
    private bool isReloading = false;
    private float reloadTime = 2f;
    
    public enum WeaponType
    {
        None,
        Pistol,
        AssaultRifle,
        SniperRifle
    }
    
    void Start()
    {
        audioSource = GetComponent<AudioSource>();
        if (audioSource == null)
        {
            audioSource = gameObject.AddComponent<AudioSource>();
        }
        
        if (firePoint == null)
        {
            // Create fire point if not assigned
            GameObject firePointObj = new GameObject("FirePoint");
            firePointObj.transform.SetParent(transform);
            firePointObj.transform.localPosition = Vector3.forward;
            firePoint = firePointObj.transform;
        }
        
        UpdateWeaponModel();
    }
    
    public bool Fire(Transform target = null)
    {
        // Check if can fire
        if (!CanFire()) return false;
        
        // Consume ammo
        currentAmmo--;
        lastFireTime = Time.time;
        
        // Determine target position
        Vector3 targetPosition;
        if (target != null)
        {
            targetPosition = target.position + Vector3.up; // Aim for center mass
        }
        else
        {
            targetPosition = firePoint.position + firePoint.forward * range;
        }
        
        // Perform raycast
        Vector3 direction = (targetPosition - firePoint.position).normalized;
        RaycastHit hit;
        bool didHit = Physics.Raycast(firePoint.position, direction, out hit, range);
        
        // Visual and audio effects
        PlayFireEffects();
        
        // Handle hit
        if (didHit)
        {
            return ProcessHit(hit);
        }
        
        return false;
    }
    
    bool CanFire()
    {
        if (currentWeapon == WeaponType.None) return false;
        if (isReloading) return false;
        if (currentAmmo <= 0) return false;
        if (Time.time - lastFireTime < 1f / fireRate) return false;
        
        return true;
    }
    
    bool ProcessHit(RaycastHit hit)
    {
        // Spawn impact effect
        if (impactEffect != null)
        {
            GameObject effect = Instantiate(impactEffect, hit.point, Quaternion.LookRotation(hit.normal));
            Destroy(effect, 2f);
        }
        
        // Check what we hit
        GameObject hitObject = hit.collider.gameObject;
        
        // Enemy hit
        EnemyAI enemy = hitObject.GetComponent<EnemyAI>();
        if (enemy != null)
        {
            enemy.TakeDamage(damage);
            Debug.Log($"Hit enemy for {damage} damage");
            return true;
        }
        
        // Enemy health system hit
        EnemyHealth enemyHealth = hitObject.GetComponent<EnemyHealth>();
        if (enemyHealth != null)
        {
            enemyHealth.TakeDamage(damage);
            Debug.Log($"Hit enemy health for {damage} damage");
            return true;
        }
        
        // Player hit (friendly fire)
        PlayerController player = hitObject.GetComponent<PlayerController>();
        if (player != null)
        {
            player.TakeDamage(damage * 0.5f); // Reduced friendly fire damage
            Debug.Log($"Friendly fire! Hit player for {damage * 0.5f} damage");
            return true;
        }
        
        // ML-Agent hit
        SquadMateAgent agent = hitObject.GetComponent<SquadMateAgent>();
        if (agent != null)
        {
            agent.TakeDamage(damage * 0.5f); // Reduced friendly fire damage
            Debug.Log($"Friendly fire! Hit agent for {damage * 0.5f} damage");
            return true;
        }
        
        return false;
    }
    
    void PlayFireEffects()
    {
        // Muzzle flash
        if (muzzleFlash != null)
        {
            GameObject flash = Instantiate(muzzleFlash, firePoint.position, firePoint.rotation);
            Destroy(flash, 0.1f);
        }
        
        // Fire sound
        if (fireSound != null && audioSource != null)
        {
            audioSource.PlayOneShot(fireSound);
        }
    }
    
    public void Reload()
    {
        if (isReloading || currentAmmo == maxAmmo) return;
        
        StartCoroutine(ReloadCoroutine());
    }
    
    System.Collections.IEnumerator ReloadCoroutine()
    {
        isReloading = true;
        
        // Play reload sound
        if (reloadSound != null && audioSource != null)
        {
            audioSource.PlayOneShot(reloadSound);
        }
        
        Debug.Log("Reloading...");
        
        yield return new WaitForSeconds(reloadTime);
        
        currentAmmo = maxAmmo;
        isReloading = false;
        
        Debug.Log("Reload complete");
    }
    
    public void EquipWeapon(WeaponType weaponType)
    {
        currentWeapon = weaponType;
        
        // Set weapon stats based on type
        switch (weaponType)
        {
            case WeaponType.Pistol:
                damage = 20f;
                range = 15f;
                fireRate = 3f;
                maxAmmo = 12;
                reloadTime = 1.5f;
                break;
                
            case WeaponType.AssaultRifle:
                damage = 25f;
                range = 25f;
                fireRate = 8f;
                maxAmmo = 30;
                reloadTime = 2f;
                break;
                
            case WeaponType.SniperRifle:
                damage = 75f;
                range = 50f;
                fireRate = 1f;
                maxAmmo = 5;
                reloadTime = 3f;
                break;
                
            case WeaponType.None:
            default:
                damage = 0f;
                range = 0f;
                fireRate = 0f;
                maxAmmo = 0;
                break;
        }
        
        currentAmmo = maxAmmo;
        UpdateWeaponModel();
        
        Debug.Log($"Equipped {weaponType}");
    }
    
    void UpdateWeaponModel()
    {
        // Hide all weapon models
        if (assaultRifleModel != null) assaultRifleModel.SetActive(false);
        if (pistolModel != null) pistolModel.SetActive(false);
        if (sniperRifleModel != null) sniperRifleModel.SetActive(false);
        
        // Show current weapon model
        switch (currentWeapon)
        {
            case WeaponType.Pistol:
                if (pistolModel != null) pistolModel.SetActive(true);
                break;
            case WeaponType.AssaultRifle:
                if (assaultRifleModel != null) assaultRifleModel.SetActive(true);
                break;
            case WeaponType.SniperRifle:
                if (sniperRifleModel != null) sniperRifleModel.SetActive(true);
                break;
        }
    }
    
    public void AddAmmo(int amount)
    {
        currentAmmo = Mathf.Min(currentAmmo + amount, maxAmmo);
        Debug.Log($"Added {amount} ammo. Current: {currentAmmo}/{maxAmmo}");
    }
    
    public bool HasWeapon()
    {
        return currentWeapon != WeaponType.None;
    }
    
    public bool NeedsAmmo()
    {
        return currentAmmo < maxAmmo * 0.3f; // Need ammo if below 30%
    }
    
    public bool IsEmpty()
    {
        return currentAmmo <= 0;
    }
    
    public float GetAmmoPercentage()
    {
        if (maxAmmo == 0) return 0f;
        return (float)currentAmmo / maxAmmo;
    }
    
    // Auto-reload when empty
    void Update()
    {
        if (currentAmmo <= 0 && !isReloading && currentWeapon != WeaponType.None)
        {
            if (emptySound != null && audioSource != null)
            {
                audioSource.PlayOneShot(emptySound);
            }
            
            Reload();
        }
    }
    
    // Public getters for ML-Agent observations
    public float GetDamage() => damage;
    public float GetRange() => range;
    public float GetFireRate() => fireRate;
    public int GetCurrentAmmo() => currentAmmo;
    public int GetMaxAmmo() => maxAmmo;
    public bool GetIsReloading() => isReloading;
    public WeaponType GetWeaponType() => currentWeapon;
}
