{"agentName": "SquadMateAlpha", "version": "1.0", "description": "Decision tree for AI teammate behavior in Unity 6 SquadMate system", "states": {"voiceCommand": {"description": "Player has issued a voice command - highest priority", "conditions": {"followCommand": {"action": "follow_player_immediately", "priority": 15, "description": "Player commanded 'Follow' - override all other behaviors"}, "reviveCommand": {"action": "revive_player_immediately", "priority": 15, "description": "Player commanded 'Revive' - prioritize revival"}, "attackCommand": {"action": "engage_combat_immediately", "priority": 15, "description": "Player commanded 'Attack' - engage nearest enemy"}, "defendCommand": {"action": "take_defensive_position", "priority": 15, "description": "Player commanded 'Defend' - find cover and protect"}, "reloadCommand": {"action": "resupply_immediately", "priority": 15, "description": "Player commanded 'Reload' - find ammo or resupply"}, "formationCommand": {"action": "move_to_formation", "priority": 15, "description": "Player commanded 'Formation' - organize squad"}}}, "playerDown": {"description": "Player is downed and needs revival", "conditions": {"enemyNearby": {"action": "seek_cover", "priority": 10, "description": "Enemy within 10m of downed player - take cover first"}, "enemyFar": {"action": "revive_player", "priority": 9, "description": "No immediate enemy threat - revive player"}, "multipleEnemies": {"action": "eliminate_threats_first", "priority": 8, "description": "Multiple enemies present - clear area before revive"}}}, "playerHealthy": {"description": "Player is alive and healthy", "conditions": {"enemyNearby": {"action": "engage_combat", "priority": 8, "description": "Enemy detected - engage in combat"}, "noEnemy": {"action": "follow_player", "priority": 3, "description": "No threats - follow and support player"}, "lootNearby": {"action": "collect_loot", "priority": 4, "description": "Valuable loot detected - collect if safe"}, "playerMovingFast": {"action": "sprint_to_catch_up", "priority": 5, "description": "Player moving quickly - sprint to maintain formation"}}}, "lowHealth": {"description": "AI teammate has low health", "conditions": {"medkitNearby": {"action": "collect_medkit", "priority": 7, "description": "Medkit available - prioritize healing"}, "noMedkit": {"action": "seek_cover", "priority": 6, "description": "No healing available - find cover and avoid combat"}, "criticalHealth": {"action": "retreat_to_player", "priority": 9, "description": "Critical health - retreat to player for protection"}}}, "combatActive": {"description": "Currently engaged in combat", "conditions": {"multipleEnemies": {"action": "prioritize_closest_threat", "priority": 8, "description": "Multiple enemies - focus on closest threat"}, "enemyLowHealth": {"action": "finish_wounded_enemy", "priority": 7, "description": "Enemy nearly dead - finish them off"}, "playerInDanger": {"action": "protect_player", "priority": 10, "description": "Player under heavy fire - prioritize player protection"}, "outOfAmmo": {"action": "find_cover_reload", "priority": 6, "description": "No ammo - find cover and look for ammo"}}}, "exploration": {"description": "Exploring area with player", "conditions": {"unknownArea": {"action": "scout_ahead", "priority": 4, "description": "New area - scout for threats and loot"}, "lootDetected": {"action": "investigate_loot", "priority": 5, "description": "Loot spotted - investigate if safe"}, "suspiciousSound": {"action": "investigate_sound", "priority": 6, "description": "Unusual sound detected - investigate cautiously"}}}}, "actions": {"follow_player_immediately": {"description": "Override all behaviors and follow player immediately", "duration": "5_seconds", "requirements": ["voice_command_active", "player_alive"], "priority_override": true}, "revive_player_immediately": {"description": "Drop everything and revive player now", "duration": "until_revive_complete", "requirements": ["voice_command_active", "player_downed"], "priority_override": true}, "engage_combat_immediately": {"description": "Attack nearest enemy immediately", "duration": "5_seconds", "requirements": ["voice_command_active", "enemy_available"], "priority_override": true}, "take_defensive_position": {"description": "Find cover and defend current area", "duration": "5_seconds", "requirements": ["voice_command_active", "cover_available"], "priority_override": true}, "resupply_immediately": {"description": "Find ammo or reload position", "duration": "5_seconds", "requirements": ["voice_command_active"], "priority_override": true}, "move_to_formation": {"description": "Move to designated formation position", "duration": "until_in_formation", "requirements": ["voice_command_active", "formation_available"], "priority_override": true}, "seek_cover": {"description": "Find nearest cover and take defensive position", "duration": "until_threat_cleared", "requirements": ["cover_available", "threat_present"]}, "revive_player": {"description": "Move to player and perform revival", "duration": "3_seconds", "requirements": ["player_downed", "safe_area"]}, "engage_combat": {"description": "Attack detected enemies", "duration": "until_enemy_defeated", "requirements": ["enemy_in_range", "weapon_ready"]}, "follow_player": {"description": "Maintain formation with player", "duration": "continuous", "requirements": ["player_alive", "no_immediate_threats"]}, "collect_loot": {"description": "Move to and collect nearby loot items", "duration": "2_seconds", "requirements": ["loot_nearby", "safe_to_collect"]}, "collect_medkit": {"description": "Prioritize collecting health items", "duration": "2_seconds", "requirements": ["medkit_available", "health_below_50"]}, "eliminate_threats_first": {"description": "Clear all enemies before other actions", "duration": "until_area_clear", "requirements": ["multiple_enemies", "combat_capable"]}, "protect_player": {"description": "Position between player and threats", "duration": "until_player_safe", "requirements": ["player_in_danger", "able_to_intercept"]}, "sprint_to_catch_up": {"description": "Increase movement speed to maintain formation", "duration": "until_in_formation", "requirements": ["player_distance_exceeded", "stamina_available"]}, "retreat_to_player": {"description": "Fall back to player position for safety", "duration": "until_near_player", "requirements": ["health_critical", "player_alive"]}}, "priorities": {"survival": 10, "player_safety": 9, "combat_effectiveness": 8, "mission_objectives": 7, "resource_management": 6, "exploration": 5, "formation_keeping": 4, "loot_collection": 3, "idle_behavior": 2, "cosmetic_actions": 1}, "learning_parameters": {"success_weight": 1.2, "failure_penalty": 0.8, "adaptation_rate": 0.1, "memory_duration": 300, "experience_threshold": 10}, "voice_responses": {"enemy_detected": ["Contact!", "Enemy spotted!", "Hostile in sight!"], "engaging": ["Engaging!", "Taking the shot!", "Opening fire!"], "player_down": ["Player down!", "Need to revive!", "Cover me!"], "reviving": ["Reviving now!", "Hang in there!", "Almost got you!"], "loot_found": ["Found something!", "Loot here!", "Useful item!"], "low_health": ["Taking damage!", "Need healing!", "I'm hurt!"], "area_clear": ["All clear!", "Area secure!", "No more hostiles!"], "following": ["Right behind you!", "On your six!", "Following!"], "acknowledgment": ["<PERSON>!", "Copy that!", "Understood!"]}, "metadata": {"created_date": "2024-12-19", "last_modified": "2024-12-19", "author": "Augment Agent", "compatible_version": "Unity 6.x", "ai_system": "SquadMate AI v1.0"}}