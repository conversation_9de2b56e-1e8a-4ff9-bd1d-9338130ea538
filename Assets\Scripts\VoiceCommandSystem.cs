using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Voice Command System for AI teammates
/// Supports hotkey simulation and future STT integration
/// </summary>
public class VoiceCommandSystem : MonoBehaviour
{
    [Header("Command Settings")]
    public bool enableVoiceCommands = true;
    public float commandCooldown = 1f;
    public bool showCommandUI = true;
    
    [Header("Command Keys")]
    public KeyCode followKey = KeyCode.Alpha1;
    public KeyCode reviveKey = KeyCode.Alpha2;
    public KeyCode attackKey = KeyCode.Alpha3;
    public KeyCode defendKey = KeyCode.Alpha4;
    public KeyCode reloadKey = KeyCode.Alpha5;
    public KeyCode formationKey = KeyCode.Alpha6;
    
    [Header("Audio Feedback")]
    public AudioClip commandReceivedSound;
    public AudioClip commandExecutedSound;
    
    public CommandType currentCommand = CommandType.None;
    public CommandType lastCommand = CommandType.None;
    private float lastCommandTime = 0f;
    private AudioSource audioSource;
    private AIActionLogger actionLogger;
    
    // Command queue for multiple AI coordination
    private Queue<CommandData> commandQueue = new Queue<CommandData>();
    
    public enum CommandType
    {
        None,
        Follow,      // "Come here" / "Follow me"
        Revive,      // "Revive me" / "Help"
        Attack,      // "Attack" / "Engage"
        Defend,      // "Defend" / "Take cover"
        Reload,      // "Reload" / "Resupply"
        Formation    // "Formation" / "Regroup"
    }
    
    [System.Serializable]
    public struct CommandData
    {
        public CommandType command;
        public Vector3 targetPosition;
        public Transform targetObject;
        public float timestamp;
        public string voiceText; // For future STT integration
    }
    
    void Start()
    {
        audioSource = GetComponent<AudioSource>();
        if (audioSource == null)
        {
            audioSource = gameObject.AddComponent<AudioSource>();
        }
        
        actionLogger = FindObjectOfType<AIActionLogger>();
        if (actionLogger == null)
        {
            GameObject loggerObj = new GameObject("AIActionLogger");
            actionLogger = loggerObj.AddComponent<AIActionLogger>();
        }
        
        Debug.Log("🎤 Voice Command System initialized");
        Debug.Log("Commands: 1=Follow, 2=Revive, 3=Attack, 4=Defend, 5=Reload, 6=Formation");
    }
    
    void Update()
    {
        if (!enableVoiceCommands) return;
        
        // Check for command cooldown
        if (Time.time - lastCommandTime < commandCooldown) return;
        
        // Process hotkey inputs (simulating voice commands)
        ProcessHotkeyCommands();
        
        // Process any queued commands
        ProcessCommandQueue();
    }
    
    void ProcessHotkeyCommands()
    {
        CommandType newCommand = CommandType.None;
        
        if (Input.GetKeyDown(followKey))
        {
            newCommand = CommandType.Follow;
        }
        else if (Input.GetKeyDown(reviveKey))
        {
            newCommand = CommandType.Revive;
        }
        else if (Input.GetKeyDown(attackKey))
        {
            newCommand = CommandType.Attack;
        }
        else if (Input.GetKeyDown(defendKey))
        {
            newCommand = CommandType.Defend;
        }
        else if (Input.GetKeyDown(reloadKey))
        {
            newCommand = CommandType.Reload;
        }
        else if (Input.GetKeyDown(formationKey))
        {
            newCommand = CommandType.Formation;
        }
        
        if (newCommand != CommandType.None)
        {
            IssueCommand(newCommand);
        }
    }
    
    public void IssueCommand(CommandType command, Vector3 targetPos = default, Transform target = null, string voiceText = "")
    {
        if (Time.time - lastCommandTime < commandCooldown) return;
        
        CommandData commandData = new CommandData
        {
            command = command,
            targetPosition = targetPos == default ? GetPlayerPosition() : targetPos,
            targetObject = target,
            timestamp = Time.time,
            voiceText = voiceText
        };
        
        // Set current command
        lastCommand = currentCommand;
        currentCommand = command;
        lastCommandTime = Time.time;
        
        // Add to queue for processing
        commandQueue.Enqueue(commandData);
        
        // Audio feedback
        PlayCommandSound(commandReceivedSound);
        
        // Log the command
        if (actionLogger != null)
        {
            actionLogger.LogCommand("Player", command.ToString(), commandData);
        }
        
        // Visual feedback
        Debug.Log($"🎤 Voice Command Issued: {command}");
        
        // Notify all AI teammates
        NotifyAITeammates(commandData);
    }
    
    void ProcessCommandQueue()
    {
        while (commandQueue.Count > 0)
        {
            CommandData command = commandQueue.Dequeue();
            ExecuteCommand(command);
        }
    }
    
    void ExecuteCommand(CommandData commandData)
    {
        // Find all AI teammates
        TeammateAI[] teammates = FindObjectsOfType<TeammateAI>();
        
        foreach (TeammateAI teammate in teammates)
        {
            if (teammate != null)
            {
                // Send command to AI
                VoiceCommandReceiver receiver = teammate.GetComponent<VoiceCommandReceiver>();
                if (receiver == null)
                {
                    receiver = teammate.gameObject.AddComponent<VoiceCommandReceiver>();
                }
                
                receiver.ReceiveCommand(commandData);
            }
        }
        
        // Audio feedback for execution
        PlayCommandSound(commandExecutedSound);
        
        Debug.Log($"✅ Command Executed: {commandData.command}");
    }
    
    void NotifyAITeammates(CommandData commandData)
    {
        // Notify audio announcers
        AIAudioAnnouncer[] announcers = FindObjectsOfType<AIAudioAnnouncer>();
        foreach (AIAudioAnnouncer announcer in announcers)
        {
            if (announcer != null)
            {
                announcer.RespondToCommand(commandData.command.ToString());
            }
        }
    }
    
    Vector3 GetPlayerPosition()
    {
        PlayerController player = FindObjectOfType<PlayerController>();
        return player != null ? player.transform.position : Vector3.zero;
    }
    
    void PlayCommandSound(AudioClip clip)
    {
        if (clip != null && audioSource != null)
        {
            audioSource.PlayOneShot(clip);
        }
    }
    
    // Public methods for UI integration
    public void IssueFollowCommand()
    {
        IssueCommand(CommandType.Follow);
    }
    
    public void IssueReviveCommand()
    {
        IssueCommand(CommandType.Revive);
    }
    
    public void IssueAttackCommand()
    {
        IssueCommand(CommandType.Attack);
    }
    
    public void IssueDefendCommand()
    {
        IssueCommand(CommandType.Defend);
    }
    
    public void IssueReloadCommand()
    {
        IssueCommand(CommandType.Reload);
    }
    
    public void IssueFormationCommand()
    {
        IssueCommand(CommandType.Formation);
    }
    
    // Future STT integration point
    public void ProcessSpeechInput(string speechText)
    {
        CommandType detectedCommand = ParseSpeechToCommand(speechText);
        if (detectedCommand != CommandType.None)
        {
            IssueCommand(detectedCommand, default, null, speechText);
        }
    }
    
    CommandType ParseSpeechToCommand(string speechText)
    {
        string text = speechText.ToLower();
        
        if (text.Contains("follow") || text.Contains("come here") || text.Contains("come to me"))
            return CommandType.Follow;
        if (text.Contains("revive") || text.Contains("help me") || text.Contains("heal"))
            return CommandType.Revive;
        if (text.Contains("attack") || text.Contains("engage") || text.Contains("fire"))
            return CommandType.Attack;
        if (text.Contains("defend") || text.Contains("cover") || text.Contains("protect"))
            return CommandType.Defend;
        if (text.Contains("reload") || text.Contains("ammo") || text.Contains("resupply"))
            return CommandType.Reload;
        if (text.Contains("formation") || text.Contains("regroup") || text.Contains("organize"))
            return CommandType.Formation;
            
        return CommandType.None;
    }
    
    // Getters for external systems
    public CommandType GetCurrentCommand() => currentCommand;
    public CommandType GetLastCommand() => lastCommand;
    public bool HasActiveCommand() => currentCommand != CommandType.None;
    public float GetTimeSinceLastCommand() => Time.time - lastCommandTime;
    
    void OnGUI()
    {
        if (!showCommandUI) return;
        
        // Command panel
        GUILayout.BeginArea(new Rect(Screen.width - 250, 10, 240, 200));
        GUILayout.Label("🎤 Voice Commands");
        
        if (GUILayout.Button($"1 - Follow ({followKey})"))
            IssueFollowCommand();
        if (GUILayout.Button($"2 - Revive ({reviveKey})"))
            IssueReviveCommand();
        if (GUILayout.Button($"3 - Attack ({attackKey})"))
            IssueAttackCommand();
        if (GUILayout.Button($"4 - Defend ({defendKey})"))
            IssueDefendCommand();
        if (GUILayout.Button($"5 - Reload ({reloadKey})"))
            IssueReloadCommand();
        if (GUILayout.Button($"6 - Formation ({formationKey})"))
            IssueFormationCommand();
        
        GUILayout.Space(10);
        GUILayout.Label($"Current: {currentCommand}");
        GUILayout.Label($"Last: {lastCommand}");
        
        GUILayout.EndArea();
    }
}
