# 🎮 PUBG-Style Unity ML-Agents Training System

## 🚀 **Complete Tactical AI Training Environment**

Train intelligent AI teammates for PUBG-style combat scenarios using Unity ML-Agents with PPO (Proximal Policy Optimization).

---

## 📋 **Requirements**

### **Software:**
- **Unity 2022.3 LTS** or newer
- **Python 3.8-3.10** (ML-Agents compatibility)
- **PyTorch 1.7.1+** 
- **ML-Agents Toolkit 0.30.0+**

### **Hardware:**
- **GPU Recommended:** NVIDIA GTX 1060+ or RTX series
- **RAM:** 8GB minimum, 16GB recommended
- **Storage:** 5GB free space

---

## 🛠 **Installation Guide**

### **1. Install Python Dependencies**
```bash
# Create virtual environment
python -m venv mlagents-env
source mlagents-env/bin/activate  # On Windows: mlagents-env\Scripts\activate

# Install ML-Agents
pip install mlagents==0.30.0
pip install torch torchvision torchaudio

# Verify installation
mlagents-learn --help
```

### **2. Unity Setup**
```bash
# Install ML-Agents Unity Package
# Window > Package Manager > Add package from git URL:
# com.unity.ml-agents
```

### **3. Project Setup**
1. Import all scripts into your Unity project
2. Create the training scene with `PUBGTrainingEnvironment`
3. Setup prefabs for Agent, Enemy, Weapons, and Loot
4. Configure NavMesh for the training arena

---

## 🎯 **Training Configuration**

### **Agent Observations (25 total):**
- **Agent State (8):** Position, forward direction, health, weapon status
- **Player State (5):** Position, distance, downed status
- **Enemy State (4):** Nearest enemy position and distance
- **Loot State (4):** Nearest loot position and distance
- **Environment (4):** Zone info, time remaining

### **Agent Actions:**
- **Continuous (3):** Move X, Move Z, Rotate
- **Discrete (1):** Action type (Move=0, Attack=1, Loot=2, Revive=3)

### **Reward System:**
```csharp
// Positive Rewards
+1.0  Follow player (per second)
+5.0  Hit enemy
+10.0 Kill enemy
+2.0  Collect loot
+10.0 Revive player
+0.1  Survival (per second)

// Negative Rewards
-10.0 Agent death
-5.0  Player death
-0.1  Damage taken
-1.0  Too far from player
```

---

## 🏃‍♂️ **Training Commands**

### **Basic Training:**
```bash
mlagents-learn config/squadmate-ppo.yaml --run-id=SquadMateBasic
```

### **Resume Training:**
```bash
mlagents-learn config/squadmate-ppo.yaml --run-id=SquadMateBasic --resume
```

### **Multi-Environment Training:**
```bash
mlagents-learn config/squadmate-ppo.yaml --run-id=SquadMateMulti --num-envs=4
```

### **Training with TensorBoard:**
```bash
mlagents-learn config/squadmate-ppo.yaml --run-id=SquadMateTB --tensorboard-log-dir=./tensorboard

# View training progress
tensorboard --logdir=./tensorboard
```

### **Export Trained Model:**
```bash
mlagents-learn config/squadmate-ppo.yaml --run-id=SquadMateExport --inference --load-model
```

---

## 🎮 **Scene Setup Guide**

### **1. Create Training Arena:**
```csharp
// Create empty GameObject with PUBGTrainingEnvironment script
// Set arena size: 50x10x50 units
// Add NavMesh to the terrain
```

### **2. Setup Spawn Points:**
- **Player Spawn:** 1-2 points
- **Agent Spawn:** 2-4 points  
- **Enemy Spawn:** 4-8 points
- **Loot Spawn:** 10-20 points

### **3. Configure Prefabs:**
```csharp
// SquadMate Agent Prefab:
- SquadMateAgent.cs (Behavior Type: "SquadMate")
- WeaponSystem.cs
- HealthSystem.cs
- Rigidbody + Collider
- NavMeshAgent

// Enemy Prefab:
- EnemyAI.cs
- EnemyHealth.cs
- WeaponSystem.cs
- NavMeshAgent

// Weapon Prefabs:
- WeaponPickup.cs
- Appropriate 3D models
- Trigger colliders
```

---

## 📊 **Training Phases**

### **Phase 1: Basic Movement (0-200k steps)**
- Learn to follow player
- Basic survival
- Simple loot collection

### **Phase 2: Combat Training (200k-800k steps)**
- Enemy detection and engagement
- Weapon usage
- Tactical positioning

### **Phase 3: Advanced Tactics (800k-2M steps)**
- Complex decision making
- Team coordination
- Revive mechanics
- Zone awareness

---

## 🧠 **Curriculum Learning**

The training uses curriculum learning to gradually increase difficulty:

```yaml
curriculum:
  - enemy_count: 1 (threshold: 0.5 reward)
  - enemy_count: 2 (threshold: 1.0 reward)  
  - enemy_count: 3 (threshold: 1.5 reward)
```

---

## 📈 **Monitoring Training**

### **Key Metrics to Watch:**
- **Cumulative Reward:** Should increase over time
- **Episode Length:** Longer episodes = better survival
- **Success Rate:** Percentage of episodes with positive outcome
- **Policy Loss:** Should decrease and stabilize

### **TensorBoard Graphs:**
- Environment/Cumulative Reward
- Environment/Episode Length
- Losses/Policy Loss
- Policy/Learning Rate

---

## 🎯 **Testing Trained Model**

### **1. Export Model:**
```bash
# Model will be saved as: results/SquadMateRun/SquadMate.onnx
```

### **2. Use in Unity:**
1. Drag `.onnx` file to `Assets/Models/`
2. Assign to `Behavior Parameters` component
3. Set `Behavior Type` to "Inference Only"
4. Test in play mode

### **3. Integration with Existing Systems:**
```csharp
// The trained agent works with:
- Voice Command System
- Action Logger
- Squad Coordination
- Tactical UI
```

---

## 🔧 **Troubleshooting**

### **Common Issues:**

**Training not starting:**
```bash
# Check Python environment
python --version
pip list | grep mlagents

# Verify Unity ML-Agents package
```

**Low performance:**
```bash
# Reduce visual quality in training
# Use --num-envs=1 for single environment
# Check GPU utilization
```

**Agent not learning:**
```bash
# Check reward signals in TensorBoard
# Verify observation space (should be 25)
# Ensure proper episode resets
```

---

## 🚀 **Advanced Features**

### **Self-Play Training:**
```yaml
self_play:
  save_steps: 50000
  team_change: 100000
  play_against_latest_model_ratio: 0.5
```

### **Behavioral Cloning:**
```bash
# Record demonstrations first
# Then use in config:
behavioral_cloning:
  demo_path: demonstrations/SquadMateDemo.demo
  strength: 0.5
```

### **Parameter Randomization:**
```yaml
parameter_randomization:
  enemy_count:
    sampler-type: uniform
    min_value: 1
    max_value: 4
```

---

## 🎉 **Expected Results**

After **2M training steps** (~24-48 hours), your AI should:

- ✅ **Follow player** intelligently
- ✅ **Engage enemies** effectively  
- ✅ **Collect loot** strategically
- ✅ **Revive player** when needed
- ✅ **Survive combat** scenarios
- ✅ **Coordinate** with team

**Your PUBG-style AI teammate is ready for deployment! 🎖️**

---

## 📞 **Support & Integration**

This ML-Agent system integrates seamlessly with:
- **Voice Command System** (hotkeys + future STT)
- **Action Logger** (training data analysis)
- **Squad Coordination** (multi-agent scenarios)
- **Augment Agent Mode** (behavior expansion)

**Ready to train the ultimate tactical AI squad! 🚀**
