using UnityEngine;

/// <summary>
/// Health system for ML-Agents and other entities
/// Handles health, damage, healing, and death
/// </summary>
public class HealthSystem : MonoBehaviour
{
    [Header("Health Settings")]
    public float maxHealth = 100f;
    public float currentHealth = 100f;
    public bool canRegenerate = false;
    public float regenerationRate = 5f; // HP per second
    public float regenerationDelay = 5f; // Delay after taking damage
    
    [Header("Visual Feedback")]
    public bool showHealthBar = true;
    public Color healthBarColor = Color.green;
    public Color healthBarBackground = Color.red;
    public Vector3 healthBarOffset = Vector3.up * 2.5f;
    
    [Header("Audio")]
    public AudioClip damageSound;
    public AudioClip healSound;
    public AudioClip deathSound;
    public AudioClip lowHealthSound;
    
    [Header("Effects")]
    public GameObject damageEffect;
    public GameObject healEffect;
    public GameObject deathEffect;
    
    private AudioSource audioSource;
    private float lastDamageTime = 0f;
    private bool isDead = false;
    private bool isLowHealth = false;
    private float lowHealthThreshold = 0.3f;
    
    // Events
    public System.Action<float> OnHealthChanged;
    public System.Action<float> OnDamageTaken;
    public System.Action<float> OnHealed;
    public System.Action OnDeath;
    public System.Action OnLowHealth;
    
    void Start()
    {
        currentHealth = maxHealth;
        audioSource = GetComponent<AudioSource>();
        
        if (audioSource == null)
        {
            audioSource = gameObject.AddComponent<AudioSource>();
            audioSource.spatialBlend = 1f; // 3D sound
        }
    }
    
    void Update()
    {
        // Health regeneration
        if (canRegenerate && !isDead && currentHealth < maxHealth)
        {
            if (Time.time - lastDamageTime > regenerationDelay)
            {
                Heal(regenerationRate * Time.deltaTime, false);
            }
        }
        
        // Low health warning
        CheckLowHealth();
    }
    
    public void TakeDamage(float damage)
    {
        if (isDead || damage <= 0f) return;
        
        float actualDamage = Mathf.Min(damage, currentHealth);
        currentHealth -= actualDamage;
        currentHealth = Mathf.Clamp(currentHealth, 0f, maxHealth);
        lastDamageTime = Time.time;
        
        Debug.Log($"{gameObject.name} took {actualDamage} damage. Health: {currentHealth}/{maxHealth}");
        
        // Visual effects
        StartCoroutine(FlashDamage());
        
        // Audio feedback
        if (damageSound != null && audioSource != null)
        {
            audioSource.PlayOneShot(damageSound);
        }
        
        // Damage effect
        if (damageEffect != null)
        {
            GameObject effect = Instantiate(damageEffect, transform.position, Quaternion.identity);
            Destroy(effect, 2f);
        }
        
        // Events
        OnDamageTaken?.Invoke(actualDamage);
        OnHealthChanged?.Invoke(currentHealth);
        
        // Check for death
        if (currentHealth <= 0f)
        {
            Die();
        }
    }
    
    public void Heal(float amount, bool playEffects = true)
    {
        if (isDead || amount <= 0f || currentHealth >= maxHealth) return;
        
        float actualHeal = Mathf.Min(amount, maxHealth - currentHealth);
        currentHealth += actualHeal;
        currentHealth = Mathf.Clamp(currentHealth, 0f, maxHealth);
        
        Debug.Log($"{gameObject.name} healed {actualHeal} HP. Health: {currentHealth}/{maxHealth}");
        
        if (playEffects)
        {
            // Audio feedback
            if (healSound != null && audioSource != null)
            {
                audioSource.PlayOneShot(healSound);
            }
            
            // Heal effect
            if (healEffect != null)
            {
                GameObject effect = Instantiate(healEffect, transform.position, Quaternion.identity);
                Destroy(effect, 2f);
            }
        }
        
        // Events
        OnHealed?.Invoke(actualHeal);
        OnHealthChanged?.Invoke(currentHealth);
    }
    
    void Die()
    {
        if (isDead) return;
        
        isDead = true;
        
        Debug.Log($"{gameObject.name} died!");
        
        // Audio feedback
        if (deathSound != null && audioSource != null)
        {
            audioSource.PlayOneShot(deathSound);
        }
        
        // Death effect
        if (deathEffect != null)
        {
            GameObject effect = Instantiate(deathEffect, transform.position, Quaternion.identity);
            Destroy(effect, 5f);
        }
        
        // Events
        OnDeath?.Invoke();
        
        // Handle different entity types
        HandleDeath();
    }
    
    void HandleDeath()
    {
        // ML-Agent death
        SquadMateAgent agent = GetComponent<SquadMateAgent>();
        if (agent != null)
        {
            agent.TakeDamage(currentHealth); // This will end the episode
            return;
        }
        
        // Enemy death
        EnemyAI enemy = GetComponent<EnemyAI>();
        if (enemy != null)
        {
            enemy.OnHealthDepleted();
            return;
        }
        
        // Player death
        PlayerController player = GetComponent<PlayerController>();
        if (player != null)
        {
            // Handle player death (respawn, game over, etc.)
            Debug.Log("Player died!");
            return;
        }
        
        // Default: destroy object after delay
        Destroy(gameObject, 3f);
    }
    
    void CheckLowHealth()
    {
        bool currentlyLowHealth = GetHealthPercentage() <= lowHealthThreshold;
        
        if (currentlyLowHealth && !isLowHealth)
        {
            isLowHealth = true;
            
            // Play low health sound
            if (lowHealthSound != null && audioSource != null)
            {
                audioSource.PlayOneShot(lowHealthSound);
            }
            
            OnLowHealth?.Invoke();
        }
        else if (!currentlyLowHealth && isLowHealth)
        {
            isLowHealth = false;
        }
    }
    
    System.Collections.IEnumerator FlashDamage()
    {
        Renderer renderer = GetComponent<Renderer>();
        if (renderer != null)
        {
            Color originalColor = renderer.material.color;
            renderer.material.color = Color.red;
            yield return new WaitForSeconds(0.1f);
            renderer.material.color = originalColor;
        }
    }
    
    public void ResetHealth()
    {
        currentHealth = maxHealth;
        isDead = false;
        isLowHealth = false;
        lastDamageTime = 0f;
        
        OnHealthChanged?.Invoke(currentHealth);
    }
    
    public void SetMaxHealth(float newMaxHealth)
    {
        float healthPercentage = GetHealthPercentage();
        maxHealth = newMaxHealth;
        currentHealth = maxHealth * healthPercentage;
        
        OnHealthChanged?.Invoke(currentHealth);
    }
    
    // Public getters
    public float GetHealthPercentage()
    {
        return maxHealth > 0 ? currentHealth / maxHealth : 0f;
    }
    
    public bool IsAlive()
    {
        return !isDead && currentHealth > 0f;
    }
    
    public bool IsDead()
    {
        return isDead;
    }
    
    public bool IsLowHealth()
    {
        return isLowHealth;
    }
    
    public bool IsFullHealth()
    {
        return currentHealth >= maxHealth;
    }
    
    public float GetCurrentHealth()
    {
        return currentHealth;
    }
    
    public float GetMaxHealth()
    {
        return maxHealth;
    }
    
    public float GetMissingHealth()
    {
        return maxHealth - currentHealth;
    }
    
    void OnGUI()
    {
        if (!showHealthBar || isDead) return;
        
        // Calculate screen position
        Vector3 screenPos = Camera.main.WorldToScreenPoint(transform.position + healthBarOffset);
        
        if (screenPos.z > 0) // Only show if in front of camera
        {
            float healthPercentage = GetHealthPercentage();
            
            // Health bar background
            GUI.color = healthBarBackground;
            GUI.DrawTexture(new Rect(screenPos.x - 25, Screen.height - screenPos.y - 5, 50, 8), Texture2D.whiteTexture);
            
            // Health bar foreground
            GUI.color = Color.Lerp(Color.red, healthBarColor, healthPercentage);
            float healthWidth = 50 * healthPercentage;
            GUI.DrawTexture(new Rect(screenPos.x - 25, Screen.height - screenPos.y - 5, healthWidth, 8), Texture2D.whiteTexture);
            
            // Health text
            GUI.color = Color.white;
            GUI.Label(new Rect(screenPos.x - 25, Screen.height - screenPos.y - 20, 50, 15), 
                     $"{currentHealth:F0}/{maxHealth:F0}", 
                     new GUIStyle { alignment = TextAnchor.MiddleCenter, fontSize = 10 });
            
            GUI.color = Color.white;
        }
    }
    
    void OnDrawGizmosSelected()
    {
        // Draw health bar position
        Gizmos.color = Color.green;
        Gizmos.DrawWireCube(transform.position + healthBarOffset, new Vector3(2f, 0.2f, 0.1f));
    }
}
