# ML-Agents Training Configuration for SquadMate AI
# PUBG-style tactical AI teammate training with PPO

behaviors:
  SquadMate:
    trainer_type: ppo
    
    # Training Parameters
    max_steps: 2000000  # 2M steps for complex tactical behavior
    time_horizon: 128   # Longer horizon for tactical decisions
    summary_freq: 10000 # Summary every 10k steps
    
    # PPO Hyperparameters
    hyperparameters:
      batch_size: 2048      # Large batch for stable learning
      buffer_size: 20480    # 10x batch size
      learning_rate: 3.0e-4 # Standard learning rate
      beta: 5.0e-3          # Entropy coefficient
      epsilon: 0.2          # PPO clip parameter
      lambd: 0.95           # GAE lambda
      num_epoch: 3          # PPO epochs per update
      learning_rate_schedule: linear # Decay learning rate
    
    # Network Architecture
    network_settings:
      normalize: true       # Normalize observations
      hidden_units: 256     # Larger network for complex behavior
      num_layers: 3         # Deep network
      vis_encode_type: simple # Visual encoding type
      memory:
        sequence_length: 64 # LSTM memory for temporal decisions
        memory_size: 128    # Memory capacity
    
    # Reward Signals
    reward_signals:
      extrinsic:
        gamma: 0.995        # High discount for long-term planning
        strength: 1.0       # Extrinsic reward weight
      
      # Curiosity-driven exploration
      curiosity:
        gamma: 0.99
        strength: 0.02      # Small curiosity bonus
        network_settings:
          normalize: true
          hidden_units: 128
          num_layers: 2
        learning_rate: 3.0e-4
    
    # Behavioral Cloning (optional - for pre-training)
    behavioral_cloning:
      demo_path: demonstrations/SquadMateDemo.demo
      strength: 0.5
      steps: 50000
    
    # Self-Play (for multi-agent scenarios)
    self_play:
      save_steps: 50000
      team_change: 100000
      swap_steps: 10000
      window: 10
      play_against_latest_model_ratio: 0.5
      initial_elo: 1200.0

# Environment Settings
env_settings:
  env_path: null  # Use Unity Editor
  env_args: null
  base_port: 5005
  num_envs: 1
  num_areas: 1
  seed: -1
  max_lifetime_restarts: 10
  restarts_rate_limit_n: 1
  restarts_rate_limit_period_s: 60

# Engine Settings
engine_settings:
  width: 1280
  height: 720
  quality_level: 1
  time_scale: 20.0  # Speed up training
  target_frame_rate: 60
  capture_frame_rate: 60

# Checkpoint Settings
checkpoint_settings:
  run_id: SquadMateRun
  initialize_from: null
  load_model: false
  resume: false
  force: false
  train_model: true
  inference: false
  
# Torch Settings (for PyTorch backend)
torch_settings:
  device: null  # Auto-detect GPU/CPU

# Additional Training Configurations

# Configuration for different training phases
training_phases:
  
  # Phase 1: Basic Movement and Following (0-200k steps)
  basic_movement:
    max_steps: 200000
    reward_weights:
      follow: 2.0
      survival: 1.0
      combat: 0.5
      loot: 0.5
      revive: 1.0
  
  # Phase 2: Combat Training (200k-800k steps)
  combat_training:
    max_steps: 800000
    reward_weights:
      follow: 1.0
      survival: 1.0
      combat: 3.0
      loot: 1.0
      revive: 1.5
  
  # Phase 3: Advanced Tactics (800k-2M steps)
  advanced_tactics:
    max_steps: 2000000
    reward_weights:
      follow: 1.0
      survival: 1.5
      combat: 2.0
      loot: 1.5
      revive: 2.0

# Curriculum Learning
curriculum:
  - name: enemy_count
    completion_criteria:
      measure: reward
      behavior: SquadMate
      signal_smoothing: true
      min_lesson_length: 100
      threshold: 0.5
    value: 1
  
  - name: enemy_count
    completion_criteria:
      measure: reward
      behavior: SquadMate
      signal_smoothing: true
      min_lesson_length: 100
      threshold: 1.0
    value: 2
  
  - name: enemy_count
    completion_criteria:
      measure: reward
      behavior: SquadMate
      signal_smoothing: true
      min_lesson_length: 100
      threshold: 1.5
    value: 3

# Parameter Randomization for Robust Training
parameter_randomization:
  resampling-interval: 5000
  
  # Randomize enemy spawn positions
  enemy_spawn_radius:
    sampler-type: uniform
    min_value: 5.0
    max_value: 20.0
  
  # Randomize enemy count
  enemy_count:
    sampler-type: uniform
    min_value: 1
    max_value: 4
  
  # Randomize loot spawn count
  loot_count:
    sampler-type: uniform
    min_value: 3
    max_value: 8
  
  # Randomize map size
  map_scale:
    sampler-type: uniform
    min_value: 0.8
    max_value: 1.2
  
  # Randomize lighting conditions
  lighting_intensity:
    sampler-type: uniform
    min_value: 0.5
    max_value: 1.5

# Training Commands Reference:
# 
# Basic Training:
# mlagents-learn config/squadmate-ppo.yaml --run-id=SquadMateBasic
#
# Resume Training:
# mlagents-learn config/squadmate-ppo.yaml --run-id=SquadMateBasic --resume
#
# Training with Curriculum:
# mlagents-learn config/squadmate-ppo.yaml --run-id=SquadMateCurriculum --curriculum=config/curriculum.yaml
#
# Multi-Environment Training:
# mlagents-learn config/squadmate-ppo.yaml --run-id=SquadMateMulti --num-envs=4
#
# Training with TensorBoard:
# mlagents-learn config/squadmate-ppo.yaml --run-id=SquadMateTB --tensorboard-log-dir=./tensorboard
#
# Export Model:
# mlagents-learn config/squadmate-ppo.yaml --run-id=SquadMateExport --inference --load-model
