using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Manages multiple AI teammates as a coordinated squad
/// </summary>
public class SquadManager : MonoBehaviour
{
    [Header("Squad Settings")]
    public GameObject botPrefab;
    public Transform[] spawnPoints;
    public int maxSquadSize = 4;
    public bool autoSpawnOnStart = true;
    
    [Header("Formation Settings")]
    public FormationType formationType = FormationType.Follow;
    public float formationSpacing = 3f;
    public float maxFormationDistance = 15f;
    
    [Header("Coordination Settings")]
    public bool enableSquadCoordination = true;
    public float coordinationRange = 20f;
    public float reviveCoordinationDelay = 1f; // Prevent multiple bots reviving same target
    
    [Header("Squad Communication")]
    public bool enableSquadChatter = true;
    public float chatterCooldown = 5f;
    
    private List<TeammateAI> squadMembers = new List<TeammateAI>();
    private PlayerController player;
    private Dictionary<TeammateAI, Vector3> formationPositions = new Dictionary<TeammateAI, Vector3>();
    private float lastChatterTime = 0f;
    
    public enum FormationType
    {
        Follow,      // Simple follow behind player
        Line,        // Line formation
        Wedge,       // V formation
        Diamond,     // Diamond formation
        Circle       // Circular formation
    }
    
    void Start()
    {
        player = FindObjectOfType<PlayerController>();
        
        if (autoSpawnOnStart)
        {
            SpawnSquad();
        }
        
        // Start coordination updates
        if (enableSquadCoordination)
        {
            InvokeRepeating(nameof(UpdateSquadCoordination), 1f, 0.5f);
        }
    }
    
    public void SpawnSquad()
    {
        if (botPrefab == null)
        {
            Debug.LogError("SquadManager: No bot prefab assigned!");
            return;
        }
        
        int spawnCount = Mathf.Min(maxSquadSize, spawnPoints.Length);
        
        for (int i = 0; i < spawnCount; i++)
        {
            Vector3 spawnPos = spawnPoints.Length > i ? spawnPoints[i].position : GetRandomSpawnPosition();
            SpawnSquadMember(spawnPos, i + 1);
        }
        
        Debug.Log($"Squad deployed: {squadMembers.Count} members");
    }
    
    TeammateAI SpawnSquadMember(Vector3 position, int memberNumber)
    {
        GameObject botInstance = Instantiate(botPrefab, position, Quaternion.identity);
        botInstance.name = $"SquadMate_{memberNumber}";
        
        TeammateAI teammate = botInstance.GetComponent<TeammateAI>();
        if (teammate != null)
        {
            squadMembers.Add(teammate);
            
            // Add squad coordination component
            SquadMemberCoordinator coordinator = botInstance.GetComponent<SquadMemberCoordinator>();
            if (coordinator == null)
            {
                coordinator = botInstance.AddComponent<SquadMemberCoordinator>();
            }
            coordinator.Initialize(this, memberNumber);
            
            // Setup audio announcer
            AIAudioAnnouncer audioAnnouncer = botInstance.GetComponent<AIAudioAnnouncer>();
            if (audioAnnouncer == null)
            {
                audioAnnouncer = botInstance.AddComponent<AIAudioAnnouncer>();
            }
            
            Debug.Log($"Squad member {memberNumber} deployed at {position}");
        }
        
        return teammate;
    }
    
    Vector3 GetRandomSpawnPosition()
    {
        if (player != null)
        {
            // Spawn around player
            Vector3 randomOffset = Random.insideUnitSphere * 5f;
            randomOffset.y = 0f;
            return player.transform.position + randomOffset;
        }
        
        return Vector3.zero;
    }
    
    void UpdateSquadCoordination()
    {
        if (squadMembers.Count == 0) return;
        
        // Remove null/destroyed members
        squadMembers.RemoveAll(member => member == null);
        
        // Update formation positions
        UpdateFormationPositions();
        
        // Coordinate special actions
        CoordinateReviveActions();
        CoordinateCombatActions();
        
        // Squad chatter
        if (enableSquadChatter && Time.time - lastChatterTime > chatterCooldown)
        {
            TriggerSquadChatter();
        }
    }
    
    void UpdateFormationPositions()
    {
        if (player == null) return;
        
        formationPositions.Clear();
        
        for (int i = 0; i < squadMembers.Count; i++)
        {
            Vector3 formationPos = CalculateFormationPosition(i, squadMembers.Count);
            formationPositions[squadMembers[i]] = formationPos;
        }
    }
    
    Vector3 CalculateFormationPosition(int memberIndex, int totalMembers)
    {
        if (player == null) return Vector3.zero;
        
        Vector3 playerPos = player.transform.position;
        Vector3 playerForward = player.transform.forward;
        Vector3 playerRight = player.transform.right;
        
        switch (formationType)
        {
            case FormationType.Follow:
                return playerPos - playerForward * (2f + memberIndex * formationSpacing);
                
            case FormationType.Line:
                float lineOffset = (memberIndex - (totalMembers - 1) * 0.5f) * formationSpacing;
                return playerPos - playerForward * 3f + playerRight * lineOffset;
                
            case FormationType.Wedge:
                if (memberIndex == 0)
                    return playerPos - playerForward * 2f;
                else
                {
                    float side = (memberIndex % 2 == 1) ? 1f : -1f;
                    float depth = (memberIndex + 1) * 0.5f;
                    return playerPos - playerForward * (2f + depth * formationSpacing) + 
                           playerRight * side * depth * formationSpacing;
                }
                
            case FormationType.Diamond:
                if (totalMembers >= 4)
                {
                    switch (memberIndex)
                    {
                        case 0: return playerPos - playerForward * 2f; // Front
                        case 1: return playerPos + playerRight * formationSpacing; // Right
                        case 2: return playerPos - playerRight * formationSpacing; // Left
                        case 3: return playerPos - playerForward * 4f; // Back
                        default: return playerPos - playerForward * (4f + (memberIndex - 3) * formationSpacing);
                    }
                }
                break;
                
            case FormationType.Circle:
                float angle = (360f / totalMembers) * memberIndex * Mathf.Deg2Rad;
                Vector3 circlePos = new Vector3(Mathf.Sin(angle), 0f, Mathf.Cos(angle)) * formationSpacing;
                return playerPos + circlePos;
        }
        
        // Default fallback
        return playerPos - playerForward * (2f + memberIndex * formationSpacing);
    }
    
    void CoordinateReviveActions()
    {
        if (player == null) return;
        
        // Check if player needs revival
        ReviveSystem reviveSystem = player.GetComponent<ReviveSystem>();
        if (reviveSystem != null && reviveSystem.isDown)
        {
            // Find closest available squad member
            TeammateAI closestMember = GetClosestAvailableMember(player.transform.position);
            if (closestMember != null)
            {
                // Assign revive task to closest member only
                // Other members should provide cover or clear threats
                AssignReviveTask(closestMember);
                AssignCoverTasks(closestMember);
            }
        }
    }
    
    void CoordinateCombatActions()
    {
        // Find all enemies in the area
        EnemyAI[] enemies = FindObjectsOfType<EnemyAI>();
        if (enemies.Length == 0) return;
        
        // Assign different enemies to different squad members to avoid clustering
        for (int i = 0; i < squadMembers.Count && i < enemies.Length; i++)
        {
            if (squadMembers[i] != null && enemies[i] != null)
            {
                // This would require extending TeammateAI to accept target assignments
                // For now, just ensure they don't all target the same enemy
            }
        }
    }
    
    TeammateAI GetClosestAvailableMember(Vector3 position)
    {
        TeammateAI closest = null;
        float closestDistance = float.MaxValue;
        
        foreach (TeammateAI member in squadMembers)
        {
            if (member == null) continue;
            
            float distance = Vector3.Distance(member.transform.position, position);
            if (distance < closestDistance)
            {
                closest = member;
                closestDistance = distance;
            }
        }
        
        return closest;
    }
    
    void AssignReviveTask(TeammateAI member)
    {
        // This would require extending TeammateAI to accept specific task assignments
        Debug.Log($"{member.name} assigned to revive player");
    }
    
    void AssignCoverTasks(TeammateAI revivingMember)
    {
        foreach (TeammateAI member in squadMembers)
        {
            if (member != null && member != revivingMember)
            {
                // Assign cover/overwatch tasks
                Debug.Log($"{member.name} assigned to provide cover");
            }
        }
    }
    
    void TriggerSquadChatter()
    {
        if (squadMembers.Count == 0) return;
        
        TeammateAI randomMember = squadMembers[Random.Range(0, squadMembers.Count)];
        if (randomMember != null)
        {
            AIAudioAnnouncer audioAnnouncer = randomMember.GetComponent<AIAudioAnnouncer>();
            if (audioAnnouncer != null)
            {
                // Random squad chatter
                string[] chatterOptions = { "follow", "acknowledge", "status" };
                string randomChatter = chatterOptions[Random.Range(0, chatterOptions.Length)];
                audioAnnouncer.RespondToCommand(randomChatter);
            }
        }
        
        lastChatterTime = Time.time;
    }
    
    // Public methods for external control
    public void SetFormationType(FormationType newFormation)
    {
        formationType = newFormation;
        UpdateFormationPositions();
    }
    
    public void AddSquadMember(Vector3 spawnPosition)
    {
        if (squadMembers.Count < maxSquadSize)
        {
            SpawnSquadMember(spawnPosition, squadMembers.Count + 1);
        }
    }
    
    public void RemoveSquadMember(TeammateAI member)
    {
        if (squadMembers.Contains(member))
        {
            squadMembers.Remove(member);
            if (member != null)
            {
                Destroy(member.gameObject);
            }
        }
    }
    
    public Vector3 GetFormationPosition(TeammateAI member)
    {
        return formationPositions.ContainsKey(member) ? formationPositions[member] : member.transform.position;
    }
    
    public int GetSquadSize()
    {
        return squadMembers.Count;
    }
    
    public List<TeammateAI> GetSquadMembers()
    {
        return new List<TeammateAI>(squadMembers);
    }
}
