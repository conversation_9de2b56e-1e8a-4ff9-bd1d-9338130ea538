using UnityEngine;

/// <summary>
/// Quick setup script for the enhanced AI teammate system
/// Automatically configures all the new components and features
/// </summary>
public class EnhancedAISetup : MonoBehaviour
{
    [Header("Setup Options")]
    public bool setupSquadManager = true;
    public bool createLootItems = true;
    public bool setupAudioSystem = true;
    public bool setupVoiceCommands = true;
    public bool setupActionLogger = true;
    public bool setupTacticalUI = true;
    public bool setupMLAgentEnvironment = true;
    public bool createWeaponPickups = true;
    public int numberOfLootItems = 5;
    public int numberOfWeapons = 3;
    
    [Header("Prefab References")]
    public GameObject teammateAIPrefab;
    public Transform[] squadSpawnPoints;
    
    [Header("Audio Clips (Optional)")]
    public AudioClip[] spawnSounds;
    public AudioClip[] combatSounds;
    public AudioClip[] supportSounds;
    
    void Start()
    {
        Debug.Log("🚀 Starting Enhanced AI System Setup...");
        
        if (setupSquadManager)
        {
            SetupSquadManager();
        }
        
        if (createLootItems)
        {
            CreateLootItems();
        }
        
        if (setupAudioSystem)
        {
            SetupAudioSystem();
        }

        if (setupVoiceCommands)
        {
            SetupVoiceCommandSystem();
        }

        if (setupActionLogger)
        {
            SetupActionLogger();
        }

        if (setupTacticalUI)
        {
            SetupTacticalUI();
        }

        if (setupMLAgentEnvironment)
        {
            SetupMLAgentEnvironment();
        }

        if (createWeaponPickups)
        {
            CreateWeaponPickups();
        }

        Debug.Log("✅ Enhanced AI System Setup Complete!");
    }
    
    void SetupSquadManager()
    {
        // Check if SquadManager already exists
        SquadManager existingManager = FindObjectOfType<SquadManager>();
        if (existingManager != null)
        {
            Debug.Log("SquadManager already exists, skipping creation");
            return;
        }
        
        // Create SquadManager
        GameObject squadManagerObj = new GameObject("SquadManager");
        SquadManager squadManager = squadManagerObj.AddComponent<SquadManager>();
        
        // Configure squad manager
        squadManager.botPrefab = teammateAIPrefab;
        squadManager.spawnPoints = squadSpawnPoints;
        squadManager.maxSquadSize = squadSpawnPoints != null ? squadSpawnPoints.Length : 2;
        squadManager.autoSpawnOnStart = false; // Let user control spawning
        
        Debug.Log("✅ SquadManager created and configured");
    }
    
    void CreateLootItems()
    {
        for (int i = 0; i < numberOfLootItems; i++)
        {
            // Random position around the scene
            Vector3 randomPos = new Vector3(
                Random.Range(-20f, 20f),
                1f,
                Random.Range(-20f, 20f)
            );
            
            // Random loot type
            LootItem.LootType randomType = (LootItem.LootType)Random.Range(0, System.Enum.GetValues(typeof(LootItem.LootType)).Length);
            
            // Create loot item
            GameObject lootObj = LootItem.CreateLootItem(randomType, randomPos, Random.Range(1, 4));
            
            Debug.Log($"Created loot: {randomType} at {randomPos}");
        }
        
        Debug.Log($"✅ Created {numberOfLootItems} loot items");
    }
    
    void SetupAudioSystem()
    {
        // Find all existing TeammateAI instances
        TeammateAI[] teammates = FindObjectsOfType<TeammateAI>();
        
        foreach (TeammateAI teammate in teammates)
        {
            SetupTeammateAudio(teammate);
        }
        
        Debug.Log($"✅ Audio system setup for {teammates.Length} teammates");
    }

    void SetupVoiceCommandSystem()
    {
        // Check if VoiceCommandSystem already exists
        VoiceCommandSystem existingVCS = FindObjectOfType<VoiceCommandSystem>();
        if (existingVCS != null)
        {
            Debug.Log("VoiceCommandSystem already exists, skipping creation");
            return;
        }

        // Create VoiceCommandSystem
        GameObject vcsObj = new GameObject("VoiceCommandSystem");
        VoiceCommandSystem vcs = vcsObj.AddComponent<VoiceCommandSystem>();

        // Configure voice command system
        vcs.enableVoiceCommands = true;
        vcs.showCommandUI = true;
        vcs.commandCooldown = 1f;

        Debug.Log("✅ Voice Command System created and configured");
    }

    void SetupActionLogger()
    {
        // Check if AIActionLogger already exists
        AIActionLogger existingLogger = FindObjectOfType<AIActionLogger>();
        if (existingLogger != null)
        {
            Debug.Log("AIActionLogger already exists, skipping creation");
            return;
        }

        // Create AIActionLogger
        GameObject loggerObj = new GameObject("AIActionLogger");
        AIActionLogger logger = loggerObj.AddComponent<AIActionLogger>();

        // Configure logger
        logger.enableLogging = true;
        logger.logToFile = true;
        logger.logToConsole = false;
        logger.maxLogEntries = 1000;

        Debug.Log("✅ AI Action Logger created and configured");
    }

    void SetupTacticalUI()
    {
        // Check if TacticalCommandPanel already exists
        TacticalCommandPanel existingPanel = FindObjectOfType<TacticalCommandPanel>();
        if (existingPanel != null)
        {
            Debug.Log("TacticalCommandPanel already exists, skipping creation");
            return;
        }

        // Create TacticalCommandPanel
        GameObject panelObj = new GameObject("TacticalCommandPanel");
        TacticalCommandPanel panel = panelObj.AddComponent<TacticalCommandPanel>();

        // Configure panel
        panel.showPanel = true;
        panel.autoHide = false;

        Debug.Log("✅ Tactical Command Panel created and configured");
    }

    void SetupMLAgentEnvironment()
    {
        // Check if PUBGTrainingEnvironment already exists
        PUBGTrainingEnvironment existingEnv = FindObjectOfType<PUBGTrainingEnvironment>();
        if (existingEnv != null)
        {
            Debug.Log("PUBGTrainingEnvironment already exists, skipping creation");
            return;
        }

        // Create PUBG Training Environment
        GameObject envObj = new GameObject("PUBGTrainingEnvironment");
        PUBGTrainingEnvironment env = envObj.AddComponent<PUBGTrainingEnvironment>();

        // Configure environment
        env.arenaSize = new Vector3(50f, 10f, 50f);
        env.randomizeEnvironment = true;
        env.episodeLength = 300f;
        env.minEnemies = 1;
        env.maxEnemies = 3;
        env.minLootItems = 5;
        env.maxLootItems = 10;

        Debug.Log("✅ ML-Agent Training Environment created and configured");
    }

    void CreateWeaponPickups()
    {
        for (int i = 0; i < numberOfWeapons; i++)
        {
            // Random position around the scene
            Vector3 randomPos = new Vector3(
                Random.Range(-15f, 15f),
                1f,
                Random.Range(-15f, 15f)
            );

            // Random weapon type
            WeaponSystem.WeaponType[] weaponTypes = {
                WeaponSystem.WeaponType.Pistol,
                WeaponSystem.WeaponType.AssaultRifle,
                WeaponSystem.WeaponType.SniperRifle
            };

            WeaponSystem.WeaponType randomType = weaponTypes[Random.Range(0, weaponTypes.Length)];

            // Create weapon pickup
            GameObject weaponObj = WeaponPickup.CreateWeaponPickup(randomType, randomPos, 30);

            Debug.Log($"Created weapon pickup: {randomType} at {randomPos}");
        }

        Debug.Log($"✅ Created {numberOfWeapons} weapon pickups");
    }

    [ContextMenu("Create ML-Agent")]
    public void CreateMLAgent()
    {
        // Create ML-Agent GameObject
        GameObject agentObj = GameObject.CreatePrimitive(PrimitiveType.Capsule);
        agentObj.name = "SquadMateAgent_ML";
        agentObj.transform.position = transform.position + Vector3.forward * 3f;
        agentObj.layer = 7; // AI layer

        // Add required components
        Rigidbody rb = agentObj.GetComponent<Rigidbody>();
        rb.constraints = RigidbodyConstraints.FreezeRotationX | RigidbodyConstraints.FreezeRotationZ;

        // Add NavMeshAgent
        UnityEngine.AI.NavMeshAgent navAgent = agentObj.AddComponent<UnityEngine.AI.NavMeshAgent>();
        navAgent.speed = 5f;
        navAgent.angularSpeed = 200f;

        // Add ML-Agent components
        SquadMateAgent mlAgent = agentObj.AddComponent<SquadMateAgent>();
        WeaponSystem weaponSystem = agentObj.AddComponent<WeaponSystem>();
        HealthSystem healthSystem = agentObj.AddComponent<HealthSystem>();

        // Add enhanced AI components
        AIAudioAnnouncer audioAnnouncer = agentObj.AddComponent<AIAudioAnnouncer>();
        SquadMemberCoordinator coordinator = agentObj.AddComponent<SquadMemberCoordinator>();
        VoiceCommandReceiver voiceReceiver = agentObj.AddComponent<VoiceCommandReceiver>();

        // Find player reference
        PlayerController player = FindObjectOfType<PlayerController>();
        if (player != null)
        {
            mlAgent.player = player.transform;
        }

        // Configure ML-Agent behavior parameters
        Unity.MLAgents.Policies.BehaviorParameters behaviorParams = agentObj.GetComponent<Unity.MLAgents.Policies.BehaviorParameters>();
        if (behaviorParams == null)
        {
            behaviorParams = agentObj.AddComponent<Unity.MLAgents.Policies.BehaviorParameters>();
        }

        behaviorParams.BehaviorName = "SquadMate";
        behaviorParams.BrainParameters.VectorObservationSize = 25;
        behaviorParams.BrainParameters.NumStackedVectorObservations = 1;
        behaviorParams.BrainParameters.VectorActionSize = new int[] { 4 }; // Discrete actions
        behaviorParams.BrainParameters.ActionSpec.NumContinuousActions = 3; // Continuous actions

        // Make it blue to distinguish from regular AI
        Renderer renderer = agentObj.GetComponent<Renderer>();
        if (renderer != null)
        {
            renderer.material.color = Color.blue;
        }

        Debug.Log("✅ ML-Agent created with all enhanced AI components");
    }
    
    void SetupTeammateAudio(TeammateAI teammate)
    {
        // Add AIAudioAnnouncer if not present
        AIAudioAnnouncer audioAnnouncer = teammate.GetComponent<AIAudioAnnouncer>();
        if (audioAnnouncer == null)
        {
            audioAnnouncer = teammate.gameObject.AddComponent<AIAudioAnnouncer>();
        }
        
        // Configure audio clips if provided
        if (spawnSounds != null && spawnSounds.Length > 0)
        {
            audioAnnouncer.spawnClips = spawnSounds;
        }
        
        if (combatSounds != null && combatSounds.Length > 0)
        {
            audioAnnouncer.attackingClips = combatSounds;
            audioAnnouncer.enemyDetectedClips = combatSounds;
        }
        
        if (supportSounds != null && supportSounds.Length > 0)
        {
            audioAnnouncer.revivingPlayerClips = supportSounds;
            audioAnnouncer.playerRevivedClips = supportSounds;
        }
        
        // Add SquadMemberCoordinator if not present
        SquadMemberCoordinator coordinator = teammate.GetComponent<SquadMemberCoordinator>();
        if (coordinator == null)
        {
            coordinator = teammate.gameObject.AddComponent<SquadMemberCoordinator>();
        }
    }
    
    [ContextMenu("Setup Enhanced AI System")]
    public void ManualSetup()
    {
        Start();
    }
    
    [ContextMenu("Create Enemy with Health")]
    public void CreateEnemyWithHealth()
    {
        // Create a test enemy
        GameObject enemy = GameObject.CreatePrimitive(PrimitiveType.Capsule);
        enemy.name = "TestEnemy";
        enemy.transform.position = new Vector3(10f, 0f, 10f);
        enemy.layer = 8; // Enemy layer
        
        // Add EnemyAI component
        EnemyAI enemyAI = enemy.AddComponent<EnemyAI>();
        
        // Add EnemyHealth component
        EnemyHealth enemyHealth = enemy.AddComponent<EnemyHealth>();
        enemyHealth.maxHealth = 100f;
        enemyHealth.showHealthBar = true;
        
        // Make it red
        Renderer renderer = enemy.GetComponent<Renderer>();
        if (renderer != null)
        {
            renderer.material.color = Color.red;
        }
        
        Debug.Log("✅ Test enemy with health system created");
    }
    
    [ContextMenu("Create Loot Samples")]
    public void CreateLootSamples()
    {
        Vector3 basePos = transform.position + Vector3.forward * 5f;
        
        // Create one of each loot type
        System.Array lootTypes = System.Enum.GetValues(typeof(LootItem.LootType));
        
        for (int i = 0; i < lootTypes.Length; i++)
        {
            LootItem.LootType lootType = (LootItem.LootType)lootTypes.GetValue(i);
            Vector3 pos = basePos + Vector3.right * i * 2f;
            
            GameObject lootObj = LootItem.CreateLootItem(lootType, pos, 1);
            Debug.Log($"Created sample {lootType} at {pos}");
        }
        
        Debug.Log("✅ Created loot samples for testing");
    }
    
    [ContextMenu("Test Squad Formation")]
    public void TestSquadFormation()
    {
        SquadManager squadManager = FindObjectOfType<SquadManager>();
        if (squadManager == null)
        {
            Debug.LogError("No SquadManager found! Run setup first.");
            return;
        }
        
        // Cycle through formation types
        SquadManager.FormationType[] formations = {
            SquadManager.FormationType.Follow,
            SquadManager.FormationType.Line,
            SquadManager.FormationType.Wedge,
            SquadManager.FormationType.Diamond,
            SquadManager.FormationType.Circle
        };
        
        SquadManager.FormationType currentFormation = formations[Random.Range(0, formations.Length)];
        squadManager.SetFormationType(currentFormation);
        
        Debug.Log($"✅ Squad formation set to: {currentFormation}");
    }
    
    void OnGUI()
    {
        // Simple UI for testing
        GUILayout.BeginArea(new Rect(10, 10, 300, 200));
        GUILayout.Label("Enhanced AI System Setup");
        
        if (GUILayout.Button("Setup All Systems"))
        {
            ManualSetup();
        }
        
        if (GUILayout.Button("Create Test Enemy"))
        {
            CreateEnemyWithHealth();
        }
        
        if (GUILayout.Button("Create Loot Samples"))
        {
            CreateLootSamples();
        }
        
        if (GUILayout.Button("Test Squad Formation"))
        {
            TestSquadFormation();
        }

        if (GUILayout.Button("Create ML-Agent"))
        {
            CreateMLAgent();
        }

        if (GUILayout.Button("Create Weapon Pickups"))
        {
            CreateWeaponPickups();
        }

        SquadManager squadManager = FindObjectOfType<SquadManager>();
        if (squadManager != null)
        {
            GUILayout.Label($"Squad Size: {squadManager.GetSquadSize()}");

            if (GUILayout.Button("Spawn Squad"))
            {
                squadManager.SpawnSquad();
            }
        }
        
        GUILayout.EndArea();
    }
}
