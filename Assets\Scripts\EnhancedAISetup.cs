using UnityEngine;

/// <summary>
/// Quick setup script for the enhanced AI teammate system
/// Automatically configures all the new components and features
/// </summary>
public class EnhancedAISetup : MonoBehaviour
{
    [Header("Setup Options")]
    public bool setupSquadManager = true;
    public bool createLootItems = true;
    public bool setupAudioSystem = true;
    public int numberOfLootItems = 5;
    
    [Header("Prefab References")]
    public GameObject teammateAIPrefab;
    public Transform[] squadSpawnPoints;
    
    [Header("Audio Clips (Optional)")]
    public AudioClip[] spawnSounds;
    public AudioClip[] combatSounds;
    public AudioClip[] supportSounds;
    
    void Start()
    {
        Debug.Log("🚀 Starting Enhanced AI System Setup...");
        
        if (setupSquadManager)
        {
            SetupSquadManager();
        }
        
        if (createLootItems)
        {
            CreateLootItems();
        }
        
        if (setupAudioSystem)
        {
            SetupAudioSystem();
        }
        
        Debug.Log("✅ Enhanced AI System Setup Complete!");
    }
    
    void SetupSquadManager()
    {
        // Check if SquadManager already exists
        SquadManager existingManager = FindObjectOfType<SquadManager>();
        if (existingManager != null)
        {
            Debug.Log("SquadManager already exists, skipping creation");
            return;
        }
        
        // Create SquadManager
        GameObject squadManagerObj = new GameObject("SquadManager");
        SquadManager squadManager = squadManagerObj.AddComponent<SquadManager>();
        
        // Configure squad manager
        squadManager.botPrefab = teammateAIPrefab;
        squadManager.spawnPoints = squadSpawnPoints;
        squadManager.maxSquadSize = squadSpawnPoints != null ? squadSpawnPoints.Length : 2;
        squadManager.autoSpawnOnStart = false; // Let user control spawning
        
        Debug.Log("✅ SquadManager created and configured");
    }
    
    void CreateLootItems()
    {
        for (int i = 0; i < numberOfLootItems; i++)
        {
            // Random position around the scene
            Vector3 randomPos = new Vector3(
                Random.Range(-20f, 20f),
                1f,
                Random.Range(-20f, 20f)
            );
            
            // Random loot type
            LootItem.LootType randomType = (LootItem.LootType)Random.Range(0, System.Enum.GetValues(typeof(LootItem.LootType)).Length);
            
            // Create loot item
            GameObject lootObj = LootItem.CreateLootItem(randomType, randomPos, Random.Range(1, 4));
            
            Debug.Log($"Created loot: {randomType} at {randomPos}");
        }
        
        Debug.Log($"✅ Created {numberOfLootItems} loot items");
    }
    
    void SetupAudioSystem()
    {
        // Find all existing TeammateAI instances
        TeammateAI[] teammates = FindObjectsOfType<TeammateAI>();
        
        foreach (TeammateAI teammate in teammates)
        {
            SetupTeammateAudio(teammate);
        }
        
        Debug.Log($"✅ Audio system setup for {teammates.Length} teammates");
    }
    
    void SetupTeammateAudio(TeammateAI teammate)
    {
        // Add AIAudioAnnouncer if not present
        AIAudioAnnouncer audioAnnouncer = teammate.GetComponent<AIAudioAnnouncer>();
        if (audioAnnouncer == null)
        {
            audioAnnouncer = teammate.gameObject.AddComponent<AIAudioAnnouncer>();
        }
        
        // Configure audio clips if provided
        if (spawnSounds != null && spawnSounds.Length > 0)
        {
            audioAnnouncer.spawnClips = spawnSounds;
        }
        
        if (combatSounds != null && combatSounds.Length > 0)
        {
            audioAnnouncer.attackingClips = combatSounds;
            audioAnnouncer.enemyDetectedClips = combatSounds;
        }
        
        if (supportSounds != null && supportSounds.Length > 0)
        {
            audioAnnouncer.revivingPlayerClips = supportSounds;
            audioAnnouncer.playerRevivedClips = supportSounds;
        }
        
        // Add SquadMemberCoordinator if not present
        SquadMemberCoordinator coordinator = teammate.GetComponent<SquadMemberCoordinator>();
        if (coordinator == null)
        {
            coordinator = teammate.gameObject.AddComponent<SquadMemberCoordinator>();
        }
    }
    
    [ContextMenu("Setup Enhanced AI System")]
    public void ManualSetup()
    {
        Start();
    }
    
    [ContextMenu("Create Enemy with Health")]
    public void CreateEnemyWithHealth()
    {
        // Create a test enemy
        GameObject enemy = GameObject.CreatePrimitive(PrimitiveType.Capsule);
        enemy.name = "TestEnemy";
        enemy.transform.position = new Vector3(10f, 0f, 10f);
        enemy.layer = 8; // Enemy layer
        
        // Add EnemyAI component
        EnemyAI enemyAI = enemy.AddComponent<EnemyAI>();
        
        // Add EnemyHealth component
        EnemyHealth enemyHealth = enemy.AddComponent<EnemyHealth>();
        enemyHealth.maxHealth = 100f;
        enemyHealth.showHealthBar = true;
        
        // Make it red
        Renderer renderer = enemy.GetComponent<Renderer>();
        if (renderer != null)
        {
            renderer.material.color = Color.red;
        }
        
        Debug.Log("✅ Test enemy with health system created");
    }
    
    [ContextMenu("Create Loot Samples")]
    public void CreateLootSamples()
    {
        Vector3 basePos = transform.position + Vector3.forward * 5f;
        
        // Create one of each loot type
        System.Array lootTypes = System.Enum.GetValues(typeof(LootItem.LootType));
        
        for (int i = 0; i < lootTypes.Length; i++)
        {
            LootItem.LootType lootType = (LootItem.LootType)lootTypes.GetValue(i);
            Vector3 pos = basePos + Vector3.right * i * 2f;
            
            GameObject lootObj = LootItem.CreateLootItem(lootType, pos, 1);
            Debug.Log($"Created sample {lootType} at {pos}");
        }
        
        Debug.Log("✅ Created loot samples for testing");
    }
    
    [ContextMenu("Test Squad Formation")]
    public void TestSquadFormation()
    {
        SquadManager squadManager = FindObjectOfType<SquadManager>();
        if (squadManager == null)
        {
            Debug.LogError("No SquadManager found! Run setup first.");
            return;
        }
        
        // Cycle through formation types
        SquadManager.FormationType[] formations = {
            SquadManager.FormationType.Follow,
            SquadManager.FormationType.Line,
            SquadManager.FormationType.Wedge,
            SquadManager.FormationType.Diamond,
            SquadManager.FormationType.Circle
        };
        
        SquadManager.FormationType currentFormation = formations[Random.Range(0, formations.Length)];
        squadManager.SetFormationType(currentFormation);
        
        Debug.Log($"✅ Squad formation set to: {currentFormation}");
    }
    
    void OnGUI()
    {
        // Simple UI for testing
        GUILayout.BeginArea(new Rect(10, 10, 300, 200));
        GUILayout.Label("Enhanced AI System Setup");
        
        if (GUILayout.Button("Setup All Systems"))
        {
            ManualSetup();
        }
        
        if (GUILayout.Button("Create Test Enemy"))
        {
            CreateEnemyWithHealth();
        }
        
        if (GUILayout.Button("Create Loot Samples"))
        {
            CreateLootSamples();
        }
        
        if (GUILayout.Button("Test Squad Formation"))
        {
            TestSquadFormation();
        }
        
        SquadManager squadManager = FindObjectOfType<SquadManager>();
        if (squadManager != null)
        {
            GUILayout.Label($"Squad Size: {squadManager.GetSquadSize()}");
            
            if (GUILayout.Button("Spawn Squad"))
            {
                squadManager.SpawnSquad();
            }
        }
        
        GUILayout.EndArea();
    }
}
