using UnityEngine;
using UnityEngine.AI;
#if UNITY_EDITOR
using UnityEditor;
using UnityEditor.AI;
#endif

/// <summary>
/// Automatic NavMesh setup system for Unity 6 AI Teammate Bot
/// Automatically creates ground, marks as Navigation Static, and bakes NavMesh
/// </summary>
public class AutoNavMeshSetup : MonoBehaviour
{
    [Header("Auto Setup")]
    public bool autoSetupOnStart = true;
    public bool createGroundIfMissing = true;
    public bool autoBakeNavMesh = true;
    
    [Header("Ground Settings")]
    public Vector3 groundSize = new Vector3(50f, 1f, 50f);
    public Vector3 groundPosition = Vector3.zero;
    public Material groundMaterial;
    
    [Header("NavMesh Settings")]
    public float agentRadius = 0.5f;
    public float agentHeight = 2f;
    public float maxSlope = 45f;
    public float stepHeight = 0.4f;
    
    void Start()
    {
        if (autoSetupOnStart)
        {
            StartCoroutine(SetupNavMeshSystem());
        }
    }
    
    System.Collections.IEnumerator SetupNavMeshSystem()
    {
        Debug.Log("🚀 Starting automatic NavMesh setup...");
        
        // Wait a frame for scene to initialize
        yield return new WaitForEndOfFrame();
        
        // Step 1: Create ground if missing
        if (createGroundIfMissing && !HasSuitableGround())
        {
            CreateGround();
            yield return new WaitForEndOfFrame();
        }
        
        // Step 2: Mark objects as Navigation Static
        MarkObjectsAsNavigationStatic();
        yield return new WaitForEndOfFrame();
        
        // Step 3: Bake NavMesh
        if (autoBakeNavMesh)
        {
            BakeNavMesh();
            yield return new WaitForSeconds(1f); // Wait for baking to complete
        }
        
        // Step 4: Validate setup
        ValidateNavMeshSetup();
        
        Debug.Log("✅ Automatic NavMesh setup complete!");
    }
    
    bool HasSuitableGround()
    {
        // Check for existing ground objects (safely check if tag exists)
        try
        {
            GameObject[] groundObjects = GameObject.FindGameObjectsWithTag("Ground");
            if (groundObjects.Length > 0) return true;
        }
        catch (UnityException)
        {
            // Ground tag doesn't exist, continue with other checks
        }
        
        // Check for objects named "Ground" or "Plane"
        GameObject ground = GameObject.Find("Ground");
        if (ground != null) return true;
        
        GameObject plane = GameObject.Find("Plane");
        if (plane != null) return true;
        
        // Check for terrain
        Terrain terrain = FindObjectOfType<Terrain>();
        if (terrain != null) return true;
        
        // Check for large flat objects
        MeshRenderer[] renderers = FindObjectsOfType<MeshRenderer>();
        foreach (MeshRenderer renderer in renderers)
        {
            if (renderer.bounds.size.x > 20f && renderer.bounds.size.z > 20f)
            {
                return true;
            }
        }
        
        return false;
    }

    void EnsureGroundTagExists()
    {
#if UNITY_EDITOR
        // Check if Ground tag exists, if not create it
        try
        {
            GameObject.FindGameObjectsWithTag("Ground");
        }
        catch (UnityException)
        {
            // Ground tag doesn't exist, create it
            UnityEditor.SerializedObject tagManager = new UnityEditor.SerializedObject(
                UnityEditor.AssetDatabase.LoadAllAssetsAtPath("ProjectSettings/TagManager.asset")[0]);
            UnityEditor.SerializedProperty tagsProp = tagManager.FindProperty("tags");

            // Add Ground tag
            tagsProp.InsertArrayElementAtIndex(0);
            UnityEditor.SerializedProperty newTagProp = tagsProp.GetArrayElementAtIndex(0);
            newTagProp.stringValue = "Ground";
            tagManager.ApplyModifiedProperties();

            Debug.Log("✅ Created 'Ground' tag");
        }
#endif
    }

    void CreateGround()
    {
        Debug.Log("🏗️ Creating ground for NavMesh...");

        // Ensure Ground tag exists
        EnsureGroundTagExists();

        // Create ground plane
        GameObject ground = GameObject.CreatePrimitive(PrimitiveType.Plane);
        ground.name = "AutoGround";

        // Set Ground tag
        try
        {
            ground.tag = "Ground";
        }
        catch (UnityException)
        {
            // Ground tag still doesn't exist, skip setting it
            Debug.Log("Ground tag not available, skipping tag assignment");
        }
        ground.transform.position = groundPosition;
        ground.transform.localScale = new Vector3(groundSize.x / 10f, 1f, groundSize.z / 10f); // Plane is 10x10 by default
        
        // Set material if provided
        if (groundMaterial != null)
        {
            Renderer renderer = ground.GetComponent<Renderer>();
            if (renderer != null)
            {
                renderer.material = groundMaterial;
            }
        }
        else
        {
            // Create a simple green material
            Renderer renderer = ground.GetComponent<Renderer>();
            if (renderer != null)
            {
                renderer.material.color = new Color(0.3f, 0.7f, 0.3f); // Green ground
            }
        }
        
        // Remove the collider since we'll use NavMesh for navigation
        Collider collider = ground.GetComponent<Collider>();
        if (collider != null)
        {
            collider.enabled = false;
        }
        
        Debug.Log($"✅ Created ground: {ground.name} at {groundPosition}");
    }
    
    void MarkObjectsAsNavigationStatic()
    {
        Debug.Log("🏷️ Marking objects as Navigation Static...");
        
        int markedCount = 0;
        
        // Find all potential ground objects
        GameObject[] allObjects = FindObjectsOfType<GameObject>();
        
        foreach (GameObject obj in allObjects)
        {
            if (ShouldMarkAsNavigationStatic(obj))
            {
                // Mark as Navigation Static
                GameObjectUtility.SetStaticEditorFlags(obj, 
                    GameObjectUtility.GetStaticEditorFlags(obj) | StaticEditorFlags.NavigationStatic);
                
                markedCount++;
                Debug.Log($"Marked as Navigation Static: {obj.name}");
            }
        }
        
        Debug.Log($"✅ Marked {markedCount} objects as Navigation Static");
    }
    
    bool ShouldMarkAsNavigationStatic(GameObject obj)
    {
        // Skip if already marked
        if ((GameObjectUtility.GetStaticEditorFlags(obj) & StaticEditorFlags.NavigationStatic) != 0)
            return false;
        
        // Skip if it's a character or AI
        if (obj.GetComponent<CharacterController>() != null) return false;
        if (obj.GetComponent<NavMeshAgent>() != null) return false;
        if (obj.GetComponent<PlayerController>() != null) return false;
        if (obj.GetComponent<TeammateAI>() != null) return false;
        if (obj.GetComponent<EnemyAI>() != null) return false;
        
        // Mark ground objects
        if (obj.name.ToLower().Contains("ground")) return true;
        if (obj.name.ToLower().Contains("plane")) return true;
        if (obj.name.ToLower().Contains("floor")) return true;

        // Check Ground tag safely
        try
        {
            if (obj.tag == "Ground") return true;
        }
        catch (UnityException)
        {
            // Ground tag doesn't exist, skip this check
        }
        
        // Mark terrain
        if (obj.GetComponent<Terrain>() != null) return true;
        
        // Mark large static objects
        MeshRenderer renderer = obj.GetComponent<MeshRenderer>();
        if (renderer != null && renderer.bounds.size.magnitude > 5f)
        {
            // Check if it's likely a static environment object
            Rigidbody rb = obj.GetComponent<Rigidbody>();
            if (rb == null || rb.isKinematic)
            {
                return true;
            }
        }
        
        return false;
    }
    
    void BakeNavMesh()
    {
        Debug.Log("🔥 Baking NavMesh...");

#if UNITY_EDITOR
        try
        {
            // Use the simple Unity Editor AI NavMesh baking
            UnityEditor.AI.NavMeshBuilder.ClearAllNavMeshes();
            UnityEditor.AI.NavMeshBuilder.BuildNavMesh();

            Debug.Log("✅ NavMesh baked successfully!");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"❌ Failed to bake NavMesh: {e.Message}");
            Debug.LogWarning("💡 Try manual baking: Window > AI > Navigation > Bake");
        }
#else
        Debug.LogWarning("⚠️ NavMesh baking only available in Editor. Please bake manually in builds.");
#endif
    }
    
    void ValidateNavMeshSetup()
    {
        Debug.Log("🔍 Validating NavMesh setup...");
        
        // Check if NavMesh exists
        NavMeshTriangulation triangulation = NavMesh.CalculateTriangulation();
        if (triangulation.vertices.Length > 0)
        {
            Debug.Log($"✅ NavMesh validation successful! {triangulation.vertices.Length} vertices");
            
            // Test NavMesh sampling
            NavMeshHit hit;
            if (NavMesh.SamplePosition(Vector3.zero, out hit, 10f, NavMesh.AllAreas))
            {
                Debug.Log($"✅ NavMesh sampling test passed at {hit.position}");
            }
            else
            {
                Debug.LogWarning("⚠️ NavMesh sampling test failed - may need larger search radius");
            }
        }
        else
        {
            Debug.LogError("❌ NavMesh validation failed - no NavMesh data found!");
        }
    }
    
    [ContextMenu("Setup NavMesh Now")]
    public void SetupNavMeshNow()
    {
        StartCoroutine(SetupNavMeshSystem());
    }
    
    [ContextMenu("Force Bake NavMesh")]
    public void ForceBakeNavMesh()
    {
        BakeNavMesh();
    }
    
    void Update()
    {
        // Manual setup hotkey
        if (Input.GetKeyDown(KeyCode.F8))
        {
            SetupNavMeshNow();
        }
    }
    
    void OnGUI()
    {
        // Check if NavMesh exists
        NavMeshTriangulation triangulation = NavMesh.CalculateTriangulation();
        bool hasNavMesh = triangulation.vertices.Length > 0;
        
        if (!hasNavMesh)
        {
            GUI.color = Color.red;
            GUI.Label(new Rect(10, 10, 400, 20), "⚠️ NO NAVMESH DETECTED");
            GUI.color = Color.yellow;
            GUI.Label(new Rect(10, 30, 400, 20), "Press F8 for automatic NavMesh setup");
            GUI.color = Color.white;
            
            if (GUI.Button(new Rect(10, 50, 200, 30), "Auto Setup NavMesh"))
            {
                SetupNavMeshNow();
            }
        }
        else
        {
            GUI.color = Color.green;
            GUI.Label(new Rect(10, 10, 400, 20), "✅ NavMesh Ready");
            GUI.color = Color.white;
        }
    }
}
