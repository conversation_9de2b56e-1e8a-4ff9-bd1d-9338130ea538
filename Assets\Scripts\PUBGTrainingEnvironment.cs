using UnityEngine;
using Unity.MLAgents;
using System.Collections.Generic;

/// <summary>
/// PUBG-style training environment for ML-Agents
/// Creates dynamic combat scenarios with loot, enemies, and objectives
/// </summary>
public class PUBGTrainingEnvironment : MonoBehaviour
{
    [Header("Environment Settings")]
    public Vector3 arenaSize = new Vector3(50f, 10f, 50f);
    public Transform arenaCenter;
    public bool randomizeEnvironment = true;
    public float episodeLength = 300f; // 5 minutes
    
    [Header("Spawn Settings")]
    public Transform[] playerSpawnPoints;
    public Transform[] agentSpawnPoints;
    public Transform[] enemySpawnPoints;
    public Transform[] lootSpawnPoints;
    
    [Header("Prefabs")]
    public GameObject playerPrefab;
    public GameObject squadMateAgentPrefab;
    public GameObject enemyPrefab;
    public GameObject[] weaponPrefabs;
    public GameObject[] lootPrefabs;
    public GameObject medkitPrefab;
    
    [Header("Dynamic Spawning")]
    public int minEnemies = 1;
    public int maxEnemies = 4;
    public int minLootItems = 5;
    public int maxLootItems = 10;
    public int minWeapons = 2;
    public int maxWeapons = 5;
    
    [Header("Environment Randomization")]
    public bool randomizeLighting = true;
    public bool randomizeWeather = false;
    public bool randomizeObstacles = true;
    public GameObject[] obstaclePrefabs;
    
    [Header("Zone Settings")]
    public bool enableShrinkingZone = false;
    public float zoneStartRadius = 25f;
    public float zoneEndRadius = 5f;
    public float zoneShrinkTime = 240f; // 4 minutes
    
    private List<GameObject> spawnedObjects = new List<GameObject>();
    private List<SquadMateAgent> agents = new List<SquadMateAgent>();
    private List<EnemyAI> enemies = new List<EnemyAI>();
    private PlayerController player;
    private float episodeStartTime;
    private bool episodeActive = false;
    
    // Zone variables
    private float currentZoneRadius;
    private Vector3 zoneCenter;
    
    void Start()
    {
        InitializeEnvironment();
    }
    
    void Update()
    {
        if (episodeActive)
        {
            UpdateEnvironment();
            CheckEpisodeEnd();
        }
    }
    
    public void InitializeEnvironment()
    {
        // Set arena center if not assigned
        if (arenaCenter == null)
        {
            arenaCenter = transform;
        }
        
        // Initialize zone
        zoneCenter = arenaCenter.position;
        currentZoneRadius = zoneStartRadius;
        
        Debug.Log("PUBG Training Environment initialized");
        
        // Start first episode
        ResetEnvironment();
    }
    
    public void ResetEnvironment()
    {
        Debug.Log("Resetting PUBG Training Environment");
        
        // Clear previous spawns
        ClearSpawnedObjects();
        
        // Reset episode state
        episodeStartTime = Time.time;
        episodeActive = true;
        currentZoneRadius = zoneStartRadius;
        
        // Randomize environment if enabled
        if (randomizeEnvironment)
        {
            RandomizeEnvironmentSettings();
        }
        
        // Spawn entities
        SpawnPlayer();
        SpawnAgents();
        SpawnEnemies();
        SpawnLoot();
        SpawnWeapons();
        
        if (randomizeObstacles)
        {
            SpawnObstacles();
        }
        
        Debug.Log($"Environment reset complete. Episode started with {enemies.Count} enemies and {agents.Count} agents");
    }
    
    void ClearSpawnedObjects()
    {
        foreach (GameObject obj in spawnedObjects)
        {
            if (obj != null)
            {
                Destroy(obj);
            }
        }
        spawnedObjects.Clear();
        agents.Clear();
        enemies.Clear();
        player = null;
    }
    
    void RandomizeEnvironmentSettings()
    {
        // Randomize lighting
        if (randomizeLighting)
        {
            Light mainLight = FindObjectOfType<Light>();
            if (mainLight != null)
            {
                mainLight.intensity = Random.Range(0.5f, 1.5f);
                mainLight.color = Color.HSVToRGB(Random.Range(0f, 1f), Random.Range(0.2f, 0.8f), 1f);
            }
        }
        
        // Randomize zone center slightly
        zoneCenter = arenaCenter.position + new Vector3(
            Random.Range(-5f, 5f),
            0f,
            Random.Range(-5f, 5f)
        );
    }
    
    void SpawnPlayer()
    {
        if (playerPrefab == null || playerSpawnPoints.Length == 0) return;
        
        Transform spawnPoint = playerSpawnPoints[Random.Range(0, playerSpawnPoints.Length)];
        GameObject playerObj = Instantiate(playerPrefab, spawnPoint.position, spawnPoint.rotation);
        player = playerObj.GetComponent<PlayerController>();
        
        spawnedObjects.Add(playerObj);
        Debug.Log($"Player spawned at {spawnPoint.position}");
    }
    
    void SpawnAgents()
    {
        if (squadMateAgentPrefab == null || agentSpawnPoints.Length == 0) return;
        
        foreach (Transform spawnPoint in agentSpawnPoints)
        {
            GameObject agentObj = Instantiate(squadMateAgentPrefab, spawnPoint.position, spawnPoint.rotation);
            SquadMateAgent agent = agentObj.GetComponent<SquadMateAgent>();
            
            if (agent != null)
            {
                agent.player = player != null ? player.transform : null;
                agents.Add(agent);
            }
            
            spawnedObjects.Add(agentObj);
        }
        
        Debug.Log($"Spawned {agents.Count} ML-Agents");
    }
    
    void SpawnEnemies()
    {
        if (enemyPrefab == null || enemySpawnPoints.Length == 0) return;
        
        int enemyCount = Random.Range(minEnemies, maxEnemies + 1);
        
        for (int i = 0; i < enemyCount && i < enemySpawnPoints.Length; i++)
        {
            Transform spawnPoint = enemySpawnPoints[Random.Range(0, enemySpawnPoints.Length)];
            GameObject enemyObj = Instantiate(enemyPrefab, spawnPoint.position, spawnPoint.rotation);
            EnemyAI enemy = enemyObj.GetComponent<EnemyAI>();
            
            if (enemy != null)
            {
                enemies.Add(enemy);
            }
            
            spawnedObjects.Add(enemyObj);
        }
        
        Debug.Log($"Spawned {enemies.Count} enemies");
    }
    
    void SpawnLoot()
    {
        if (lootPrefabs.Length == 0 || lootSpawnPoints.Length == 0) return;
        
        int lootCount = Random.Range(minLootItems, maxLootItems + 1);
        
        for (int i = 0; i < lootCount; i++)
        {
            Transform spawnPoint = lootSpawnPoints[Random.Range(0, lootSpawnPoints.Length)];
            GameObject lootPrefab = lootPrefabs[Random.Range(0, lootPrefabs.Length)];
            GameObject lootObj = Instantiate(lootPrefab, spawnPoint.position, spawnPoint.rotation);
            
            spawnedObjects.Add(lootObj);
        }
        
        // Always spawn some medkits
        if (medkitPrefab != null)
        {
            for (int i = 0; i < 3; i++)
            {
                Transform spawnPoint = lootSpawnPoints[Random.Range(0, lootSpawnPoints.Length)];
                GameObject medkitObj = Instantiate(medkitPrefab, spawnPoint.position, spawnPoint.rotation);
                spawnedObjects.Add(medkitObj);
            }
        }
        
        Debug.Log($"Spawned {lootCount} loot items and 3 medkits");
    }
    
    void SpawnWeapons()
    {
        if (weaponPrefabs.Length == 0 || lootSpawnPoints.Length == 0) return;
        
        int weaponCount = Random.Range(minWeapons, maxWeapons + 1);
        
        for (int i = 0; i < weaponCount; i++)
        {
            Transform spawnPoint = lootSpawnPoints[Random.Range(0, lootSpawnPoints.Length)];
            GameObject weaponPrefab = weaponPrefabs[Random.Range(0, weaponPrefabs.Length)];
            GameObject weaponObj = Instantiate(weaponPrefab, spawnPoint.position, spawnPoint.rotation);
            
            spawnedObjects.Add(weaponObj);
        }
        
        Debug.Log($"Spawned {weaponCount} weapons");
    }
    
    void SpawnObstacles()
    {
        if (obstaclePrefabs.Length == 0) return;
        
        int obstacleCount = Random.Range(3, 8);
        
        for (int i = 0; i < obstacleCount; i++)
        {
            Vector3 randomPos = GetRandomPositionInArena();
            GameObject obstaclePrefab = obstaclePrefabs[Random.Range(0, obstaclePrefabs.Length)];
            GameObject obstacleObj = Instantiate(obstaclePrefab, randomPos, Quaternion.identity);
            
            spawnedObjects.Add(obstacleObj);
        }
        
        Debug.Log($"Spawned {obstacleCount} obstacles");
    }
    
    void UpdateEnvironment()
    {
        // Update shrinking zone
        if (enableShrinkingZone)
        {
            UpdateShrinkingZone();
        }
        
        // Check for dynamic respawning
        CheckDynamicRespawning();
    }
    
    void UpdateShrinkingZone()
    {
        float elapsedTime = Time.time - episodeStartTime;
        float shrinkProgress = Mathf.Clamp01(elapsedTime / zoneShrinkTime);
        
        currentZoneRadius = Mathf.Lerp(zoneStartRadius, zoneEndRadius, shrinkProgress);
        
        // Apply zone damage to entities outside the zone
        ApplyZoneDamage();
    }
    
    void ApplyZoneDamage()
    {
        float zoneDamage = 5f * Time.deltaTime; // 5 damage per second
        
        // Check agents
        foreach (SquadMateAgent agent in agents)
        {
            if (agent != null && Vector3.Distance(agent.transform.position, zoneCenter) > currentZoneRadius)
            {
                agent.TakeDamage(zoneDamage);
            }
        }
        
        // Check player
        if (player != null && Vector3.Distance(player.transform.position, zoneCenter) > currentZoneRadius)
        {
            player.TakeDamage(zoneDamage);
        }
        
        // Check enemies
        foreach (EnemyAI enemy in enemies)
        {
            if (enemy != null && Vector3.Distance(enemy.transform.position, zoneCenter) > currentZoneRadius)
            {
                enemy.TakeDamage(zoneDamage);
            }
        }
    }
    
    void CheckDynamicRespawning()
    {
        // Respawn enemies if all are dead (optional)
        if (enemies.Count == 0 && Time.time - episodeStartTime > 60f)
        {
            SpawnEnemies();
        }
    }
    
    void CheckEpisodeEnd()
    {
        float elapsedTime = Time.time - episodeStartTime;
        
        // End episode conditions
        bool timeUp = elapsedTime >= episodeLength;
        bool allAgentsDead = agents.TrueForAll(agent => agent == null || agent.currentHealth <= 0);
        bool playerDead = player == null || player.currentHealth <= 0;
        bool allEnemiesDead = enemies.TrueForAll(enemy => enemy == null || enemy.currentHealth <= 0);
        
        if (timeUp || allAgentsDead || (playerDead && !HasReviveCapability()))
        {
            EndEpisode();
        }
        else if (allEnemiesDead)
        {
            // Victory condition - give bonus rewards and reset
            foreach (SquadMateAgent agent in agents)
            {
                if (agent != null)
                {
                    agent.AddReward(10f); // Victory bonus
                }
            }
            EndEpisode();
        }
    }
    
    bool HasReviveCapability()
    {
        // Check if any agent is alive and can revive
        foreach (SquadMateAgent agent in agents)
        {
            if (agent != null && agent.currentHealth > 0)
            {
                return true;
            }
        }
        return false;
    }
    
    void EndEpisode()
    {
        episodeActive = false;
        
        Debug.Log($"Episode ended after {Time.time - episodeStartTime:F1} seconds");
        
        // End episode for all agents
        foreach (SquadMateAgent agent in agents)
        {
            if (agent != null)
            {
                agent.EndEpisode();
            }
        }
        
        // Reset environment for next episode
        Invoke(nameof(ResetEnvironment), 2f);
    }
    
    Vector3 GetRandomPositionInArena()
    {
        return arenaCenter.position + new Vector3(
            Random.Range(-arenaSize.x * 0.5f, arenaSize.x * 0.5f),
            0f,
            Random.Range(-arenaSize.z * 0.5f, arenaSize.z * 0.5f)
        );
    }
    
    void OnDrawGizmos()
    {
        // Draw arena bounds
        if (arenaCenter != null)
        {
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireCube(arenaCenter.position, arenaSize);
            
            // Draw zone
            if (enableShrinkingZone)
            {
                Gizmos.color = Color.red;
                Gizmos.DrawWireSphere(zoneCenter, currentZoneRadius);
            }
        }
        
        // Draw spawn points
        Gizmos.color = Color.green;
        if (playerSpawnPoints != null)
        {
            foreach (Transform spawn in playerSpawnPoints)
            {
                if (spawn != null)
                    Gizmos.DrawWireSphere(spawn.position, 1f);
            }
        }
        
        Gizmos.color = Color.blue;
        if (agentSpawnPoints != null)
        {
            foreach (Transform spawn in agentSpawnPoints)
            {
                if (spawn != null)
                    Gizmos.DrawWireSphere(spawn.position, 1f);
            }
        }
        
        Gizmos.color = Color.red;
        if (enemySpawnPoints != null)
        {
            foreach (Transform spawn in enemySpawnPoints)
            {
                if (spawn != null)
                    Gizmos.DrawWireSphere(spawn.position, 1f);
            }
        }
    }
}
