using Unity.MLAgents;
using Unity.MLAgents.Sensors;
using Unity.MLAgents.Actuators;
using UnityEngine;
using UnityEngine.AI;

/// <summary>
/// ML-Agent for PUBG-style tactical AI teammate
/// Learns to follow, fight, loot, and revive through PPO training
/// </summary>
public class SquadMateAgent : Agent
{
    [Header("Agent Settings")]
    public Transform player;
    public float moveSpeed = 5f;
    public float rotationSpeed = 200f;
    public float maxHealth = 100f;
    public float currentHealth = 100f;
    
    [Header("Combat Settings")]
    public float attackRange = 15f;
    public float damage = 25f;
    public float fireRate = 2f;
    public LayerMask enemyLayer = 1 << 8;
    public LayerMask lootLayer = 1 << 9;
    
    [Header("Detection Settings")]
    public float visionRange = 20f;
    public float lootDetectionRange = 10f;
    public float reviveRange = 3f;
    
    [Header("Rewards")]
    public float followReward = 1f;
    public float combatReward = 5f;
    public float reviveReward = 10f;
    public float lootReward = 2f;
    public float survivalReward = 0.1f;
    
    // Components
    private Rigidbody rb;
    private NavMeshAgent navAgent;
    private WeaponSystem weaponSystem;
    private HealthSystem healthSystem;
    
    // State tracking
    private Transform currentTarget;
    private Transform currentLoot;
    private bool isReviving = false;
    private bool hasWeapon = false;
    private float lastFireTime = 0f;
    private float episodeStartTime;
    
    // Episode statistics
    private int enemiesKilled = 0;
    private int itemsLooted = 0;
    private int revivesPerformed = 0;
    private float damageDealt = 0f;
    private float damageTaken = 0f;
    
    void Start()
    {
        rb = GetComponent<Rigidbody>();
        navAgent = GetComponent<NavMeshAgent>();
        weaponSystem = GetComponent<WeaponSystem>();
        healthSystem = GetComponent<HealthSystem>();
        
        if (navAgent != null)
        {
            navAgent.speed = moveSpeed;
            navAgent.angularSpeed = rotationSpeed;
        }
    }
    
    public override void OnEpisodeBegin()
    {
        // Reset agent state
        ResetAgent();
        
        // Reset episode statistics
        enemiesKilled = 0;
        itemsLooted = 0;
        revivesPerformed = 0;
        damageDealt = 0f;
        damageTaken = 0f;
        episodeStartTime = Time.time;
        
        // Random spawn position
        Vector3 spawnPos = GetRandomSpawnPosition();
        transform.position = spawnPos;
        transform.rotation = Quaternion.identity;
        
        // Reset health
        currentHealth = maxHealth;
        if (healthSystem != null)
        {
            healthSystem.ResetHealth();
        }
        
        Debug.Log($"Episode started for {gameObject.name} at {spawnPos}");
    }
    
    public override void CollectObservations(VectorSensor sensor)
    {
        // Agent state (8 observations)
        sensor.AddObservation(transform.position); // 3
        sensor.AddObservation(transform.forward); // 3
        sensor.AddObservation(currentHealth / maxHealth); // 1
        sensor.AddObservation(hasWeapon ? 1f : 0f); // 1
        
        // Player state (5 observations)
        if (player != null)
        {
            sensor.AddObservation(player.position); // 3
            sensor.AddObservation(Vector3.Distance(transform.position, player.position)); // 1
            sensor.AddObservation(IsPlayerDowned() ? 1f : 0f); // 1
        }
        else
        {
            sensor.AddObservation(Vector3.zero); // 3
            sensor.AddObservation(0f); // 1
            sensor.AddObservation(0f); // 1
        }
        
        // Nearest enemy state (4 observations)
        Transform nearestEnemy = FindNearestEnemy();
        if (nearestEnemy != null)
        {
            sensor.AddObservation(nearestEnemy.position); // 3
            sensor.AddObservation(Vector3.Distance(transform.position, nearestEnemy.position)); // 1
        }
        else
        {
            sensor.AddObservation(Vector3.zero); // 3
            sensor.AddObservation(0f); // 1
        }
        
        // Nearest loot state (4 observations)
        Transform nearestLoot = FindNearestLoot();
        if (nearestLoot != null)
        {
            sensor.AddObservation(nearestLoot.position); // 3
            sensor.AddObservation(Vector3.Distance(transform.position, nearestLoot.position)); // 1
        }
        else
        {
            sensor.AddObservation(Vector3.zero); // 3
            sensor.AddObservation(0f); // 1
        }
        
        // Total: 25 observations
    }
    
    public override void OnActionReceived(ActionBuffers actions)
    {
        // Continuous actions
        float moveX = Mathf.Clamp(actions.ContinuousActions[0], -1f, 1f);
        float moveZ = Mathf.Clamp(actions.ContinuousActions[1], -1f, 1f);
        float rotate = Mathf.Clamp(actions.ContinuousActions[2], -1f, 1f);
        
        // Discrete actions
        int actionType = actions.DiscreteActions[0]; // 0=move, 1=attack, 2=loot, 3=revive
        
        // Execute movement
        ExecuteMovement(moveX, moveZ, rotate);
        
        // Execute discrete action
        ExecuteAction(actionType);
        
        // Calculate rewards
        CalculateRewards();
        
        // Check episode end conditions
        CheckEpisodeEnd();
    }
    
    void ExecuteMovement(float moveX, float moveZ, float rotate)
    {
        // Movement
        Vector3 movement = new Vector3(moveX, 0, moveZ) * moveSpeed * Time.fixedDeltaTime;
        movement = transform.TransformDirection(movement);
        
        if (rb != null)
        {
            rb.MovePosition(rb.position + movement);
        }
        
        // Rotation
        if (Mathf.Abs(rotate) > 0.1f)
        {
            transform.Rotate(0, rotate * rotationSpeed * Time.fixedDeltaTime, 0);
        }
    }
    
    void ExecuteAction(int actionType)
    {
        switch (actionType)
        {
            case 0: // Move/Follow
                // Already handled in movement
                break;
                
            case 1: // Attack
                TryAttack();
                break;
                
            case 2: // Loot
                TryLoot();
                break;
                
            case 3: // Revive
                TryRevive();
                break;
        }
    }
    
    void TryAttack()
    {
        if (!hasWeapon || Time.time - lastFireTime < 1f / fireRate) return;
        
        Transform enemy = FindNearestEnemy();
        if (enemy != null && Vector3.Distance(transform.position, enemy.position) <= attackRange)
        {
            // Face the enemy
            Vector3 direction = (enemy.position - transform.position).normalized;
            transform.rotation = Quaternion.LookRotation(direction);
            
            // Attack
            if (weaponSystem != null)
            {
                bool hit = weaponSystem.Fire(enemy);
                if (hit)
                {
                    AddReward(combatReward);
                    damageDealt += damage;
                    
                    // Check if enemy was killed
                    EnemyAI enemyAI = enemy.GetComponent<EnemyAI>();
                    if (enemyAI != null && enemyAI.currentHealth <= 0)
                    {
                        enemiesKilled++;
                        AddReward(combatReward * 2); // Bonus for kill
                    }
                }
            }
            
            lastFireTime = Time.time;
        }
    }
    
    void TryLoot()
    {
        Transform loot = FindNearestLoot();
        if (loot != null && Vector3.Distance(transform.position, loot.position) <= 2f)
        {
            LootItem lootItem = loot.GetComponent<LootItem>();
            if (lootItem != null)
            {
                lootItem.CollectByTeammate(null); // Collect the loot
                AddReward(lootReward);
                itemsLooted++;
                
                // Special rewards for weapon pickup
                if (lootItem.lootType == LootItem.LootType.Weapon)
                {
                    hasWeapon = true;
                    AddReward(lootReward * 2);
                }
            }
        }
    }
    
    void TryRevive()
    {
        if (player != null && IsPlayerDowned() && 
            Vector3.Distance(transform.position, player.position) <= reviveRange)
        {
            // Start reviving
            if (!isReviving)
            {
                isReviving = true;
                AddReward(reviveReward * 0.1f); // Small reward for starting revive
            }
            
            // Complete revive (simplified)
            PlayerController playerController = player.GetComponent<PlayerController>();
            if (playerController != null)
            {
                playerController.Revive();
                AddReward(reviveReward);
                revivesPerformed++;
                isReviving = false;
            }
        }
        else
        {
            isReviving = false;
        }
    }
    
    void CalculateRewards()
    {
        // Survival reward
        AddReward(survivalReward * Time.fixedDeltaTime);
        
        // Follow player reward
        if (player != null && !IsPlayerDowned())
        {
            float distance = Vector3.Distance(transform.position, player.position);
            if (distance < 5f)
            {
                AddReward(followReward * Time.fixedDeltaTime);
            }
            else if (distance > 20f)
            {
                AddReward(-followReward * Time.fixedDeltaTime); // Penalty for being too far
            }
        }
        
        // Health penalty
        if (currentHealth < maxHealth * 0.5f)
        {
            AddReward(-0.1f * Time.fixedDeltaTime);
        }
    }
    
    void CheckEpisodeEnd()
    {
        // End episode if agent dies
        if (currentHealth <= 0)
        {
            AddReward(-10f); // Death penalty
            EndEpisode();
        }
        
        // End episode if player dies and can't be revived
        if (player != null && IsPlayerDead())
        {
            AddReward(-5f); // Failed to protect player
            EndEpisode();
        }
        
        // End episode after max time
        if (Time.time - episodeStartTime > 300f) // 5 minutes
        {
            EndEpisode();
        }
    }
    
    // Helper methods
    Transform FindNearestEnemy()
    {
        Collider[] enemies = Physics.OverlapSphere(transform.position, visionRange, enemyLayer);
        Transform nearest = null;
        float nearestDistance = float.MaxValue;
        
        foreach (Collider enemy in enemies)
        {
            float distance = Vector3.Distance(transform.position, enemy.transform.position);
            if (distance < nearestDistance)
            {
                nearest = enemy.transform;
                nearestDistance = distance;
            }
        }
        
        return nearest;
    }
    
    Transform FindNearestLoot()
    {
        Collider[] loot = Physics.OverlapSphere(transform.position, lootDetectionRange, lootLayer);
        Transform nearest = null;
        float nearestDistance = float.MaxValue;
        
        foreach (Collider item in loot)
        {
            float distance = Vector3.Distance(transform.position, item.transform.position);
            if (distance < nearestDistance)
            {
                nearest = item.transform;
                nearestDistance = distance;
            }
        }
        
        return nearest;
    }
    
    bool IsPlayerDowned()
    {
        if (player == null) return false;
        ReviveSystem reviveSystem = player.GetComponent<ReviveSystem>();
        return reviveSystem != null && reviveSystem.isDown;
    }
    
    bool IsPlayerDead()
    {
        if (player == null) return true;
        PlayerController playerController = player.GetComponent<PlayerController>();
        return playerController != null && playerController.currentHealth <= 0;
    }
    
    Vector3 GetRandomSpawnPosition()
    {
        // Random position within training area
        return new Vector3(
            Random.Range(-20f, 20f),
            1f,
            Random.Range(-20f, 20f)
        );
    }
    
    void ResetAgent()
    {
        currentTarget = null;
        currentLoot = null;
        isReviving = false;
        hasWeapon = false;
        lastFireTime = 0f;
        
        if (rb != null)
        {
            rb.velocity = Vector3.zero;
            rb.angularVelocity = Vector3.zero;
        }
    }
    
    // Public methods for external systems
    public void TakeDamage(float damage)
    {
        currentHealth -= damage;
        damageTaken += damage;
        currentHealth = Mathf.Clamp(currentHealth, 0f, maxHealth);
        
        AddReward(-damage * 0.1f); // Damage penalty
        
        if (currentHealth <= 0)
        {
            AddReward(-10f); // Death penalty
            EndEpisode();
        }
    }
    
    public void OnEnemyKilled()
    {
        enemiesKilled++;
        AddReward(combatReward * 2);
    }
    
    public void OnLootCollected(LootItem.LootType lootType)
    {
        itemsLooted++;
        AddReward(lootReward);
        
        if (lootType == LootItem.LootType.Weapon)
        {
            hasWeapon = true;
        }
    }
    
    // Heuristic for manual testing
    public override void Heuristic(in ActionBuffers actionsOut)
    {
        var continuousActionsOut = actionsOut.ContinuousActions;
        var discreteActionsOut = actionsOut.DiscreteActions;
        
        // WASD movement
        continuousActionsOut[0] = Input.GetAxis("Horizontal");
        continuousActionsOut[1] = Input.GetAxis("Vertical");
        continuousActionsOut[2] = Input.GetAxis("Mouse X");
        
        // Action keys
        if (Input.GetKey(KeyCode.Space))
            discreteActionsOut[0] = 1; // Attack
        else if (Input.GetKey(KeyCode.E))
            discreteActionsOut[0] = 2; // Loot
        else if (Input.GetKey(KeyCode.F))
            discreteActionsOut[0] = 3; // Revive
        else
            discreteActionsOut[0] = 0; // Move
    }
}
