using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;

/// <summary>
/// Tactical Command Panel UI for voice commands and AI control
/// Provides visual interface for commanding AI teammates
/// </summary>
public class TacticalCommandPanel : MonoBehaviour
{
    [Header("UI References")]
    public Canvas commandCanvas;
    public Button followButton;
    public Button reviveButton;
    public Button attackButton;
    public Button defendButton;
    public Button reloadButton;
    public Button formationButton;
    
    [<PERSON><PERSON>("Status Display")]
    public Text statusText;
    public Text logText;
    public Slider commandCooldownSlider;
    
    [Header("Settings")]
    public bool showPanel = true;
    public bool autoHide = false;
    public float autoHideDelay = 5f;
    
    private VoiceCommandSystem voiceCommandSystem;
    private AIActionLogger actionLogger;
    private float lastCommandTime = 0f;
    private List<string> recentLogs = new List<string>();
    
    void Start()
    {
        InitializeUI();
        FindSystems();
    }
    
    void Update()
    {
        UpdateUI();
        UpdateStatus();
        
        // Auto-hide logic
        if (autoHide && Time.time - lastCommandTime > autoHideDelay)
        {
            SetPanelVisible(false);
        }
    }
    
    void InitializeUI()
    {
        // Create UI if not assigned
        if (commandCanvas == null)
        {
            CreateCommandUI();
        }
        
        // Setup button listeners
        if (followButton != null)
            followButton.onClick.AddListener(() => IssueCommand(VoiceCommandSystem.CommandType.Follow));
        if (reviveButton != null)
            reviveButton.onClick.AddListener(() => IssueCommand(VoiceCommandSystem.CommandType.Revive));
        if (attackButton != null)
            attackButton.onClick.AddListener(() => IssueCommand(VoiceCommandSystem.CommandType.Attack));
        if (defendButton != null)
            defendButton.onClick.AddListener(() => IssueCommand(VoiceCommandSystem.CommandType.Defend));
        if (reloadButton != null)
            reloadButton.onClick.AddListener(() => IssueCommand(VoiceCommandSystem.CommandType.Reload));
        if (formationButton != null)
            formationButton.onClick.AddListener(() => IssueCommand(VoiceCommandSystem.CommandType.Formation));
        
        SetPanelVisible(showPanel);
    }
    
    void CreateCommandUI()
    {
        // Create canvas
        GameObject canvasObj = new GameObject("TacticalCommandCanvas");
        commandCanvas = canvasObj.AddComponent<Canvas>();
        commandCanvas.renderMode = RenderMode.ScreenSpaceOverlay;
        commandCanvas.sortingOrder = 100;
        
        CanvasScaler scaler = canvasObj.AddComponent<CanvasScaler>();
        scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
        scaler.referenceResolution = new Vector2(1920, 1080);
        
        canvasObj.AddComponent<GraphicRaycaster>();
        
        // Create panel background
        GameObject panelObj = new GameObject("CommandPanel");
        panelObj.transform.SetParent(commandCanvas.transform);
        
        RectTransform panelRect = panelObj.AddComponent<RectTransform>();
        panelRect.anchorMin = new Vector2(0.8f, 0.5f);
        panelRect.anchorMax = new Vector2(0.98f, 0.9f);
        panelRect.offsetMin = Vector2.zero;
        panelRect.offsetMax = Vector2.zero;
        
        Image panelImage = panelObj.AddComponent<Image>();
        panelImage.color = new Color(0, 0, 0, 0.7f);
        
        // Create buttons
        CreateCommandButton(panelObj, "Follow", VoiceCommandSystem.CommandType.Follow, 0);
        CreateCommandButton(panelObj, "Revive", VoiceCommandSystem.CommandType.Revive, 1);
        CreateCommandButton(panelObj, "Attack", VoiceCommandSystem.CommandType.Attack, 2);
        CreateCommandButton(panelObj, "Defend", VoiceCommandSystem.CommandType.Defend, 3);
        CreateCommandButton(panelObj, "Reload", VoiceCommandSystem.CommandType.Reload, 4);
        CreateCommandButton(panelObj, "Formation", VoiceCommandSystem.CommandType.Formation, 5);
        
        // Create status text
        CreateStatusText(panelObj);
        
        Debug.Log("✅ Tactical Command Panel UI created");
    }
    
    void CreateCommandButton(GameObject parent, string buttonText, VoiceCommandSystem.CommandType commandType, int index)
    {
        GameObject buttonObj = new GameObject($"{buttonText}Button");
        buttonObj.transform.SetParent(parent.transform);
        
        RectTransform buttonRect = buttonObj.AddComponent<RectTransform>();
        float yPos = 0.9f - (index * 0.12f);
        buttonRect.anchorMin = new Vector2(0.05f, yPos - 0.08f);
        buttonRect.anchorMax = new Vector2(0.95f, yPos);
        buttonRect.offsetMin = Vector2.zero;
        buttonRect.offsetMax = Vector2.zero;
        
        Image buttonImage = buttonObj.AddComponent<Image>();
        buttonImage.color = GetCommandColor(commandType);
        
        Button button = buttonObj.AddComponent<Button>();
        button.targetGraphic = buttonImage;
        button.onClick.AddListener(() => IssueCommand(commandType));
        
        // Button text
        GameObject textObj = new GameObject("Text");
        textObj.transform.SetParent(buttonObj.transform);
        
        RectTransform textRect = textObj.AddComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.offsetMin = Vector2.zero;
        textRect.offsetMax = Vector2.zero;
        
        Text text = textObj.AddComponent<Text>();
        text.text = $"{buttonText}\n({index + 1})";
        text.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
        text.fontSize = 14;
        text.color = Color.white;
        text.alignment = TextAnchor.MiddleCenter;
        
        // Store button reference
        switch (commandType)
        {
            case VoiceCommandSystem.CommandType.Follow: followButton = button; break;
            case VoiceCommandSystem.CommandType.Revive: reviveButton = button; break;
            case VoiceCommandSystem.CommandType.Attack: attackButton = button; break;
            case VoiceCommandSystem.CommandType.Defend: defendButton = button; break;
            case VoiceCommandSystem.CommandType.Reload: reloadButton = button; break;
            case VoiceCommandSystem.CommandType.Formation: formationButton = button; break;
        }
    }
    
    void CreateStatusText(GameObject parent)
    {
        GameObject statusObj = new GameObject("StatusText");
        statusObj.transform.SetParent(parent.transform);
        
        RectTransform statusRect = statusObj.AddComponent<RectTransform>();
        statusRect.anchorMin = new Vector2(0.05f, 0.05f);
        statusRect.anchorMax = new Vector2(0.95f, 0.25f);
        statusRect.offsetMin = Vector2.zero;
        statusRect.offsetMax = Vector2.zero;
        
        statusText = statusObj.AddComponent<Text>();
        statusText.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
        statusText.fontSize = 10;
        statusText.color = Color.white;
        statusText.alignment = TextAnchor.UpperLeft;
        statusText.text = "Tactical Command Panel\nReady for orders...";
    }
    
    Color GetCommandColor(VoiceCommandSystem.CommandType commandType)
    {
        switch (commandType)
        {
            case VoiceCommandSystem.CommandType.Follow: return Color.green;
            case VoiceCommandSystem.CommandType.Revive: return Color.blue;
            case VoiceCommandSystem.CommandType.Attack: return Color.red;
            case VoiceCommandSystem.CommandType.Defend: return Color.yellow;
            case VoiceCommandSystem.CommandType.Reload: return Color.cyan;
            case VoiceCommandSystem.CommandType.Formation: return Color.magenta;
            default: return Color.gray;
        }
    }
    
    void FindSystems()
    {
        voiceCommandSystem = FindObjectOfType<VoiceCommandSystem>();
        if (voiceCommandSystem == null)
        {
            GameObject vcsObj = new GameObject("VoiceCommandSystem");
            voiceCommandSystem = vcsObj.AddComponent<VoiceCommandSystem>();
        }
        
        actionLogger = FindObjectOfType<AIActionLogger>();
        if (actionLogger == null)
        {
            GameObject loggerObj = new GameObject("AIActionLogger");
            actionLogger = loggerObj.AddComponent<AIActionLogger>();
        }
    }
    
    void IssueCommand(VoiceCommandSystem.CommandType commandType)
    {
        if (voiceCommandSystem != null)
        {
            voiceCommandSystem.IssueCommand(commandType);
            lastCommandTime = Time.time;
            
            // Visual feedback
            UpdateCommandFeedback(commandType);
            
            // Show panel if hidden
            if (!showPanel)
            {
                SetPanelVisible(true);
            }
        }
    }
    
    void UpdateCommandFeedback(VoiceCommandSystem.CommandType commandType)
    {
        string commandText = $"Command Issued: {commandType}";
        recentLogs.Add($"[{Time.time:F1}] {commandText}");
        
        // Keep only last 5 logs
        if (recentLogs.Count > 5)
        {
            recentLogs.RemoveAt(0);
        }
        
        Debug.Log($"🎤 {commandText}");
    }
    
    void UpdateUI()
    {
        if (voiceCommandSystem == null) return;
        
        // Update cooldown slider
        if (commandCooldownSlider != null)
        {
            float timeSinceCommand = voiceCommandSystem.GetTimeSinceLastCommand();
            float cooldown = 1f; // voiceCommandSystem.commandCooldown
            commandCooldownSlider.value = Mathf.Clamp01(timeSinceCommand / cooldown);
        }
        
        // Update button states
        bool canIssueCommand = voiceCommandSystem.GetTimeSinceLastCommand() > 1f;
        SetButtonsInteractable(canIssueCommand);
    }
    
    void UpdateStatus()
    {
        if (statusText == null) return;
        
        string status = "🎤 Tactical Command Panel\n";
        
        if (voiceCommandSystem != null)
        {
            status += $"Current: {voiceCommandSystem.GetCurrentCommand()}\n";
            status += $"Last: {voiceCommandSystem.GetLastCommand()}\n";
        }
        
        // Add recent logs
        status += "\nRecent Commands:\n";
        foreach (string log in recentLogs)
        {
            status += log + "\n";
        }
        
        // Add AI status
        TeammateAI[] teammates = FindObjectsOfType<TeammateAI>();
        status += $"\nAI Teammates: {teammates.Length}";
        
        statusText.text = status;
    }
    
    void SetButtonsInteractable(bool interactable)
    {
        if (followButton != null) followButton.interactable = interactable;
        if (reviveButton != null) reviveButton.interactable = interactable;
        if (attackButton != null) attackButton.interactable = interactable;
        if (defendButton != null) defendButton.interactable = interactable;
        if (reloadButton != null) reloadButton.interactable = interactable;
        if (formationButton != null) formationButton.interactable = interactable;
    }
    
    public void SetPanelVisible(bool visible)
    {
        showPanel = visible;
        if (commandCanvas != null)
        {
            commandCanvas.gameObject.SetActive(visible);
        }
    }
    
    public void TogglePanel()
    {
        SetPanelVisible(!showPanel);
    }
    
    // Hotkey for toggling panel
    void LateUpdate()
    {
        if (Input.GetKeyDown(KeyCode.Tab))
        {
            TogglePanel();
        }
    }
}
